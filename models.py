import os
import uuid
import sqlite3
import math
from datetime import datetime
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from flask import current_app
from db import get_db

def get_region_from_coordinates(lat, lng):
    """تحديد المنطقة من الإحداثيات - نسخة مبسطة"""
    # هذه نسخة مبسطة تعيد المنطقة بناءً على الإحداثيات
    # تستخدم فقط للحفاظ على توافق الكود الحالي

    # تحديد المنطقة الرئيسية بناءً على الإحداثيات التقريبية
    if 32.7 <= lat <= 33.0 and 12.9 <= lng <= 13.4:
        return "طرابلس"
    elif 31.9 <= lat <= 32.3 and 19.8 <= lng <= 20.3:
        return "بنغازي"
    elif 32.2 <= lat <= 32.5 and 14.9 <= lng <= 15.3:
        return "مصراتة"
    else:
        return "غير معروف"


class ValidationError(Exception):
    """استثناء للتحقق من صحة البيانات"""
    pass

class PendingStore:
    """نموذج المتجر المعلق"""

    @staticmethod
    def get_all():
        """الحصول على جميع المتاجر المعلقة"""
        db = get_db()
        cursor = db.cursor()

        cursor.execute('''
        SELECT ps.*, u.username as marketer_name, u.phone as marketer_phone
        FROM pending_stores ps
        LEFT JOIN users u ON ps.marketer_id = u.id
        ORDER BY ps.created_at DESC
        ''')

        stores = [dict(store) for store in cursor.fetchall()]
        return stores

    @staticmethod
    def get_by_id(store_id):
        """الحصول على متجر معلق بواسطة المعرف"""
        db = get_db()
        cursor = db.cursor()
        cursor.execute('''
        SELECT ps.*, u.username as marketer_name, u.phone as marketer_phone
        FROM pending_stores ps
        LEFT JOIN users u ON ps.marketer_id = u.id
        WHERE ps.id = ?
        ''', (store_id,))
        store = cursor.fetchone()
        return dict(store) if store else None

    @staticmethod
    def create(data):
        """إنشاء متجر معلق جديد"""
        store_id = str(uuid.uuid4())
        now = datetime.now().isoformat()

        # التحقق من وجود البيانات المطلوبة
        if not data.get('name'):
            raise ValidationError('اسم المتجر مطلوب')

        if not data.get('latitude') or not data.get('longitude'):
            raise ValidationError('موقع المتجر مطلوب')

        # معالجة الصورة إذا كانت موجودة
        image_path = None
        if 'image' in data and data['image']:
            image = data['image']
            secure_name = secure_filename(image.filename)
            filename = f"{store_id}_{secure_name}"
            upload_folder = current_app.config['UPLOAD_FOLDER']
            save_path = os.path.join(upload_folder, filename)
            image_path = os.path.join('static', 'uploads', filename)
            image.save(save_path)

        db = get_db()
        cursor = db.cursor()
        # الحصول على رقم القائمة من البيانات أو استخدام القائمة الافتراضية (1)
        list_id = int(data.get('list_id', 1))

        cursor.execute('''
        INSERT INTO pending_stores (id, name, phone, latitude, longitude, image_path, list_id, marketer_id, type, address, full_address, city_name, region_name, city_id, region_id, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            store_id,
            data['name'],
            data.get('phone', ''),
            float(data['latitude']),
            float(data['longitude']),
            image_path,
            list_id,
            data.get('marketer_id'),
            data.get('type', 'A'),  # نوع المتجر الافتراضي هو A
            data.get('address', ''),  # وصف العنوان
            data.get('full_address', ''),  # عنوان المتجر الكامل
            data.get('city_name', ''),  # اسم المدينة
            data.get('region_name', ''),  # اسم المنطقة
            data.get('city_id'),  # معرف المدينة
            data.get('region_id'),  # معرف المنطقة
            now,
            now
        ))
        db.commit()

        # إرجاع المتجر المنشأ
        return PendingStore.get_by_id(store_id)

    @staticmethod
    def delete(store_id):
        """حذف متجر معلق"""
        # الحصول على المتجر
        store = PendingStore.get_by_id(store_id)
        if not store:
            return False

        # حذف الصورة إذا كانت موجودة
        if store['image_path'] and os.path.exists(store['image_path']):
            os.remove(store['image_path'])

        # حذف المتجر من قاعدة البيانات
        db = get_db()
        cursor = db.cursor()
        cursor.execute('DELETE FROM pending_stores WHERE id = ?', (store_id,))
        db.commit()

        return True

    @staticmethod
    def approve(store_id, list_id):
        """الموافقة على متجر معلق ونقله إلى المتاجر النشطة"""
        # الحصول على المتجر المعلق
        pending_store = PendingStore.get_by_id(store_id)
        if not pending_store:
            return False

        # إنشاء متجر جديد من المتجر المعلق
        store_data = {
            'name': pending_store['name'],
            'phone': pending_store['phone'],
            'latitude': pending_store['latitude'],
            'longitude': pending_store['longitude'],
            'list_id': list_id,
            'image_path': pending_store['image_path'],
            'type': pending_store.get('type', 'A'),  # نوع المتجر
            'address': pending_store.get('address', ''),  # وصف العنوان
            'full_address': pending_store.get('full_address', ''),  # عنوان المتجر الكامل
            'city_name': pending_store.get('city_name', ''),  # اسم المدينة
            'region_name': pending_store.get('region_name', ''),  # اسم المنطقة
            'city_id': pending_store.get('city_id'),  # معرف المدينة
            'region_id': pending_store.get('region_id')  # معرف المنطقة
        }

        # إنشاء المتجر الجديد
        new_store_id = str(uuid.uuid4())
        now = datetime.now().isoformat()

        db = get_db()
        cursor = db.cursor()

        # نقل الصورة إذا كانت موجودة
        image_path = pending_store['image_path']
        if image_path:
            # إنشاء اسم ملف جديد للصورة
            old_filename = os.path.basename(image_path)
            new_filename = f"{new_store_id}_{old_filename.split('_', 1)[1]}"
            old_path = os.path.join(current_app.config['UPLOAD_FOLDER'], old_filename)
            new_path = os.path.join(current_app.config['UPLOAD_FOLDER'], new_filename)

            # نسخ الصورة
            if os.path.exists(old_path):
                import shutil
                shutil.copy2(old_path, new_path)
                image_path = os.path.join('static', 'uploads', new_filename)

        # إنشاء المتجر الجديد
        cursor.execute('''
        INSERT INTO stores (id, name, phone, latitude, longitude, image_path, list_id, type, address, full_address, city_name, region_name, city_id, region_id, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            new_store_id,
            store_data['name'],
            store_data['phone'],
            float(store_data['latitude']),
            float(store_data['longitude']),
            image_path,
            list_id,
            store_data.get('type', 'A'),  # نوع المتجر
            store_data.get('address', ''),  # وصف العنوان
            store_data.get('full_address', ''),  # عنوان المتجر الكامل
            store_data.get('city_name', ''),  # اسم المدينة
            store_data.get('region_name', ''),  # اسم المنطقة
            store_data.get('city_id'),  # معرف المدينة
            store_data.get('region_id'),  # معرف المنطقة
            now,
            now
        ))

        # حذف المتجر المعلق
        cursor.execute('DELETE FROM pending_stores WHERE id = ?', (store_id,))
        db.commit()

        # إرجاع معرف المتجر الجديد
        return new_store_id

    @staticmethod
    def reject(store_id):
        """رفض متجر معلق"""
        # حذف المتجر المعلق
        return PendingStore.delete(store_id)


class Store:
    """نموذج المتجر"""

    @staticmethod
    def get_all(list_id=None):
        """الحصول على جميع المتاجر"""
        db = get_db()
        cursor = db.cursor()

        try:
            if list_id is not None:
                cursor.execute('SELECT * FROM stores WHERE list_id = ? ORDER BY created_at DESC', (list_id,))
            else:
                cursor.execute('SELECT * FROM stores ORDER BY created_at DESC')

            stores = [dict(store) for store in cursor.fetchall()]
            return stores
        except Exception as e:
            print(f"Error in Store.get_all: {str(e)}")
            return []

    @staticmethod
    def get_lists():
        """الحصول على قائمة بجميع القوائم المتاحة"""
        db = get_db()
        cursor = db.cursor()
        cursor.execute('SELECT * FROM lists ORDER BY id')
        lists = [dict(list_item) for list_item in cursor.fetchall()]

        # إذا لم تكن هناك قوائم، إرجاع قائمة فارغة
        if not lists:
            return []

        return lists

    @staticmethod
    def get_by_id(store_id):
        """الحصول على متجر بواسطة المعرف"""
        db = get_db()
        cursor = db.cursor()
        cursor.execute('SELECT * FROM stores WHERE id = ?', (store_id,))
        store = cursor.fetchone()
        return dict(store) if store else None

    @staticmethod
    def create(data):
        """إنشاء متجر جديد"""
        store_id = str(uuid.uuid4())
        now = datetime.now().isoformat()

        # التحقق من وجود البيانات المطلوبة
        if not data.get('name'):
            raise ValidationError('اسم المتجر مطلوب')

        if not data.get('latitude') or not data.get('longitude'):
            raise ValidationError('موقع المتجر مطلوب')

        # معالجة الصورة إذا كانت موجودة
        image_path = None
        if 'image' in data and data['image']:
            image = data['image']
            secure_name = secure_filename(image.filename)
            filename = f"{store_id}_{secure_name}"
            upload_folder = current_app.config['UPLOAD_FOLDER']
            save_path = os.path.join(upload_folder, filename)
            image_path = os.path.join('static', 'uploads', filename)
            image.save(save_path)

        db = get_db()
        cursor = db.cursor()
        # الحصول على رقم القائمة من البيانات أو استخدام القائمة الافتراضية (1)
        list_id = int(data.get('list_id', 1))

        cursor.execute('''
        INSERT INTO stores (id, name, phone, latitude, longitude, image_path, list_id, type, performance_score, address, full_address, city_name, region_name, city_id, region_id, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            store_id,
            data['name'],
            data.get('phone', ''),
            float(data['latitude']),
            float(data['longitude']),
            image_path,
            list_id,
            data.get('type', 'A'),  # نوع المتجر الافتراضي هو A
            data.get('performance_score', 0.0),  # درجة الأداء الافتراضية هي 0
            data.get('address', ''),  # وصف العنوان
            data.get('full_address', ''),  # عنوان المتجر الكامل
            data.get('city_name', ''),  # اسم المدينة
            data.get('region_name', ''),  # اسم المنطقة
            data.get('city_id'),  # معرف المدينة
            data.get('region_id'),  # معرف المنطقة
            now,
            now
        ))
        db.commit()

        # إرجاع المتجر المنشأ
        return Store.get_by_id(store_id)

    @staticmethod
    def update(store_id, data):
        """تحديث متجر موجود"""
        now = datetime.now().isoformat()

        # الحصول على المتجر الحالي
        current_store = Store.get_by_id(store_id)
        if not current_store:
            return None

        # معالجة الصورة إذا كانت موجودة
        image_path = current_store['image_path']
        if 'image' in data and data['image']:
            # حذف الصورة القديمة إذا كانت موجودة
            if image_path and os.path.exists(image_path):
                os.remove(image_path)

            # حفظ الصورة الجديدة
            image = data['image']
            secure_name = secure_filename(image.filename)
            filename = f"{store_id}_{secure_name}"
            upload_folder = current_app.config['UPLOAD_FOLDER']
            save_path = os.path.join(upload_folder, filename)
            image_path = os.path.join('static', 'uploads', filename)
            image.save(save_path)

        db = get_db()
        cursor = db.cursor()
        # الحصول على رقم القائمة من البيانات أو استخدام القائمة الحالية
        list_id = int(data.get('list_id', current_store.get('list_id', 1)))

        cursor.execute('''
        UPDATE stores
        SET name = ?, phone = ?, latitude = ?, longitude = ?, image_path = ?, list_id = ?,
            type = ?, performance_score = ?, address = ?, full_address = ?, city_name = ?, region_name = ?, city_id = ?, region_id = ?, updated_at = ?
        WHERE id = ?
        ''', (
            data['name'],
            data.get('phone', ''),
            float(data['latitude']),
            float(data['longitude']),
            image_path,
            list_id,
            data.get('type', current_store.get('type', 'A')),  # استخدام النوع الحالي أو الافتراضي
            data.get('performance_score', current_store.get('performance_score', 0.0)),  # استخدام درجة الأداء الحالية أو الافتراضية
            data.get('address', current_store.get('address', '')),  # استخدام وصف العنوان الحالي أو الفارغ
            data.get('full_address', current_store.get('full_address', '')),  # استخدام عنوان المتجر الكامل الحالي أو الفارغ
            data.get('city_name', current_store.get('city_name', '')),  # اسم المدينة
            data.get('region_name', current_store.get('region_name', '')),  # اسم المنطقة
            data.get('city_id', current_store.get('city_id')),  # معرف المدينة
            data.get('region_id', current_store.get('region_id')),  # معرف المنطقة
            now,
            store_id
        ))
        db.commit()

        # إرجاع المتجر المحدث
        return Store.get_by_id(store_id)

    @staticmethod
    def delete(store_id):
        """حذف متجر"""
        # الحصول على المتجر
        store = Store.get_by_id(store_id)
        if not store:
            return False

        # حذف الصورة إذا كانت موجودة
        if store['image_path'] and os.path.exists(store['image_path']):
            os.remove(store['image_path'])

        # حذف المتجر من قاعدة البيانات
        db = get_db()
        cursor = db.cursor()
        cursor.execute('DELETE FROM stores WHERE id = ?', (store_id,))
        db.commit()

        return True

    @staticmethod
    def search(query):
        """البحث عن متاجر"""
        db = get_db()
        cursor = db.cursor()
        search_term = f"%{query}%"
        cursor.execute('''
        SELECT * FROM stores
        WHERE name LIKE ? OR phone LIKE ?
        ORDER BY created_at DESC
        ''', (search_term, search_term))
        stores = [dict(store) for store in cursor.fetchall()]
        return stores

    @staticmethod
    def transfer_to_marketer(store_id, target_marketer_id):
        """نقل متجر إلى مسوق آخر"""
        try:
            # التحقق من وجود المتجر
            store = Store.get_by_id(store_id)
            if not store:
                return False

            # التأكد من أن معرف المسوق يحتوي على البادئة المطلوبة
            if not target_marketer_id.startswith('user-'):
                target_marketer_id = f"user-{target_marketer_id}"

            # التحقق من وجود المسوق الجديد
            target_marketer = User.get_by_id(target_marketer_id)
            if not target_marketer:
                return False

            if target_marketer['role_id'] != Role.MARKETER:
                return False

            # الحصول على قائمة المسوق الجديد (أول قائمة مخصصة له)
            marketer_lists = CustomList.get_marketer_lists(target_marketer_id)
            if not marketer_lists:
                # إذا لم يكن للمسوق قوائم مخصصة، استخدم القائمة الافتراضية
                target_list_id = 1
            else:
                # استخدم أول قائمة مخصصة للمسوق
                target_list_id = marketer_lists[0]['id']

            # تحديث المتجر لنقله إلى قائمة المسوق الجديد
            db = get_db()
            cursor = db.cursor()
            now = datetime.now().isoformat()

            cursor.execute('''
            UPDATE stores
            SET list_id = ?, updated_at = ?
            WHERE id = ?
            ''', (target_list_id, now, store_id))

            affected_rows = cursor.rowcount
            db.commit()

            return affected_rows > 0

        except Exception as e:
            return False


class CustomList:
    """نموذج القوائم المخصصة"""

    @staticmethod
    def get_all():
        """الحصول على جميع القوائم المخصصة"""
        db = get_db()
        cursor = db.cursor()
        cursor.execute('SELECT * FROM lists ORDER BY id')
        lists = [dict(list_item) for list_item in cursor.fetchall()]
        return lists

    @staticmethod
    def get_by_id(list_id):
        """الحصول على قائمة بواسطة المعرف"""
        db = get_db()
        cursor = db.cursor()
        cursor.execute('SELECT * FROM lists WHERE id = ?', (list_id,))
        list_item = cursor.fetchone()
        return dict(list_item) if list_item else None

    @staticmethod
    def create(data):
        """إنشاء قائمة جديدة"""
        now = datetime.now().isoformat()

        # التحقق من وجود البيانات المطلوبة
        if not data.get('name'):
            raise ValidationError('اسم القائمة مطلوب')

        db = get_db()
        cursor = db.cursor()

        cursor.execute('''
        INSERT INTO lists (name, description, created_at, updated_at)
        VALUES (?, ?, ?, ?)
        ''', (
            data['name'],
            data.get('description', ''),
            now,
            now
        ))
        db.commit()

        # الحصول على معرف القائمة الجديدة
        cursor.execute('SELECT last_insert_rowid()')
        list_id = cursor.fetchone()[0]

        return CustomList.get_by_id(list_id)

    @staticmethod
    def update(list_id, data):
        """تحديث قائمة موجودة"""
        now = datetime.now().isoformat()

        # الحصول على القائمة الحالية
        current_list = CustomList.get_by_id(list_id)
        if not current_list:
            return None

        # التحقق من وجود البيانات المطلوبة
        if not data.get('name'):
            raise ValidationError('اسم القائمة مطلوب')

        db = get_db()
        cursor = db.cursor()

        cursor.execute('''
        UPDATE lists
        SET name = ?, description = ?, updated_at = ?
        WHERE id = ?
        ''', (
            data['name'],
            data.get('description', ''),
            now,
            list_id
        ))
        db.commit()

        return CustomList.get_by_id(list_id)

    @staticmethod
    def delete(list_id):
        """حذف قائمة"""
        # الحصول على القائمة
        list_item = CustomList.get_by_id(list_id)
        if not list_item:
            return False

        db = get_db()
        cursor = db.cursor()

        # لا يمكن حذف القائمة الافتراضية (رقم 1)
        if list_id == 1:
            return False

        # تحديث المتاجر المرتبطة بهذه القائمة لتكون في القائمة الافتراضية
        cursor.execute('UPDATE stores SET list_id = 1 WHERE list_id = ?', (list_id,))

        # حذف الروابط بين المسوقين وهذه القائمة
        cursor.execute('DELETE FROM marketer_lists WHERE list_id = ?', (list_id,))

        # حذف القائمة
        cursor.execute('DELETE FROM lists WHERE id = ?', (list_id,))
        db.commit()

        return True

    @staticmethod
    def assign_to_marketer(list_id, user_id):
        """تخصيص قائمة لمسوق"""
        now = datetime.now().isoformat()

        # التحقق من وجود القائمة والمسوق
        list_item = CustomList.get_by_id(list_id)
        user = User.get_by_id(user_id)

        if not list_item or not user:
            return False

        # التحقق من أن المستخدم مسوق
        if user['role_id'] != Role.MARKETER:
            return False

        db = get_db()
        cursor = db.cursor()

        # التحقق من عدم وجود التخصيص بالفعل
        cursor.execute('SELECT * FROM marketer_lists WHERE user_id = ? AND list_id = ?', (user_id, list_id))
        if cursor.fetchone():
            return True  # التخصيص موجود بالفعل

        # إضافة التخصيص
        cursor.execute('''
        INSERT INTO marketer_lists (user_id, list_id, created_at)
        VALUES (?, ?, ?)
        ''', (user_id, list_id, now))
        db.commit()

        return True

    @staticmethod
    def unassign_from_marketer(list_id, user_id):
        """إزالة تخصيص قائمة من مسوق"""
        db = get_db()
        cursor = db.cursor()

        # حذف التخصيص
        cursor.execute('DELETE FROM marketer_lists WHERE user_id = ? AND list_id = ?', (user_id, list_id))
        db.commit()

        return True

    @staticmethod
    def get_marketer_lists(user_id):
        """الحصول على القوائم المخصصة لمسوق"""
        db = get_db()
        cursor = db.cursor()

        cursor.execute('''
        SELECT l.* FROM lists l
        JOIN marketer_lists ml ON l.id = ml.list_id
        WHERE ml.user_id = ?
        ORDER BY l.id
        ''', (user_id,))

        lists = [dict(list_item) for list_item in cursor.fetchall()]
        return lists

    @staticmethod
    def get_list_marketer(list_id):
        """الحصول على المسوق المخصص لقائمة معينة"""
        db = get_db()
        cursor = db.cursor()

        cursor.execute('''
        SELECT u.id, u.username, u.phone
        FROM users u
        JOIN marketer_lists ml ON u.id = ml.user_id
        WHERE ml.list_id = ? AND u.role_id = 2
        LIMIT 1
        ''', (list_id,))

        marketer = cursor.fetchone()
        return dict(marketer) if marketer else None


class Role:
    """نموذج الدور"""

    # ثوابت الأدوار
    ADMIN = 1
    MARKETER = 2
    VISITOR = 3

    @staticmethod
    def get_all():
        """الحصول على جميع الأدوار"""
        db = get_db()
        cursor = db.cursor()
        cursor.execute('SELECT * FROM roles')
        roles = [dict(role) for role in cursor.fetchall()]
        return roles

    @staticmethod
    def get_by_id(role_id):
        """الحصول على دور بواسطة المعرف"""
        db = get_db()
        cursor = db.cursor()
        cursor.execute('SELECT * FROM roles WHERE id = ?', (role_id,))
        role = cursor.fetchone()
        return dict(role) if role else None


class StoreStatistics:
    """نموذج إحصائيات المتاجر"""

    @staticmethod
    def get_all(filters=None):
        """الحصول على جميع إحصائيات المتاجر مع إمكانية التصفية"""
        db = get_db()
        cursor = db.cursor()

        query = '''
        SELECT ss.*, s.name as store_name, s.type as store_type
        FROM store_statistics ss
        JOIN stores s ON ss.store_id = s.id
        '''

        params = []
        where_clauses = []

        # إضافة شروط التصفية إذا كانت موجودة
        if filters:
            if 'store_id' in filters:
                where_clauses.append('ss.store_id = ?')
                params.append(filters['store_id'])

            if 'region' in filters:
                where_clauses.append('ss.region = ?')
                params.append(filters['region'])

            if 'store_type' in filters:
                where_clauses.append('s.type = ?')
                params.append(filters['store_type'])

        # إضافة شروط WHERE إذا كانت موجودة
        if where_clauses:
            query += ' WHERE ' + ' AND '.join(where_clauses)

        # إضافة الترتيب
        query += ' ORDER BY ss.date DESC'

        cursor.execute(query, params)
        stats = [dict(stat) for stat in cursor.fetchall()]
        return stats

    @staticmethod
    def get_by_id(stat_id):
        """الحصول على إحصائية بواسطة المعرف"""
        db = get_db()
        cursor = db.cursor()
        cursor.execute('''
        SELECT ss.*, s.name as store_name, s.type as store_type
        FROM store_statistics ss
        JOIN stores s ON ss.store_id = s.id
        WHERE ss.id = ?
        ''', (stat_id,))
        stat = cursor.fetchone()
        return dict(stat) if stat else None

    @staticmethod
    def create(data):
        """إنشاء إحصائية جديدة"""
        now = datetime.now().isoformat()

        # التحقق من وجود البيانات المطلوبة
        if not data.get('store_id'):
            raise ValidationError('معرف المتجر مطلوب')

        if not data.get('date'):
            data['date'] = now.split('T')[0]  # استخدام التاريخ الحالي

        db = get_db()
        cursor = db.cursor()

        cursor.execute('''
        INSERT INTO store_statistics (
            store_id, region, date, visits, sales, performance_score,
            weather_condition, event_name, created_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['store_id'],
            data.get('region', ''),
            data['date'],
            data.get('visits', 0),
            data.get('sales', 0.0),
            data.get('performance_score', 0.0),
            data.get('weather_condition', ''),
            data.get('event_name', ''),
            now
        ))
        db.commit()

        # الحصول على معرف الإحصائية الجديدة
        cursor.execute('SELECT last_insert_rowid()')
        stat_id = cursor.fetchone()[0]

        return StoreStatistics.get_by_id(stat_id)

    @staticmethod
    def update_store_performance(store_id, performance_score):
        """تحديث درجة أداء المتجر"""
        db = get_db()
        cursor = db.cursor()

        cursor.execute('''
        UPDATE stores
        SET performance_score = ?
        WHERE id = ?
        ''', (performance_score, store_id))
        db.commit()

        return True

    @staticmethod
    def get_store_stats_by_region():
        """الحصول على إحصائيات المتاجر مجمعة حسب المنطقة"""
        db = get_db()
        cursor = db.cursor()

        cursor.execute('''
        SELECT
            region,
            COUNT(DISTINCT store_id) as store_count,
            SUM(visits) as total_visits,
            SUM(sales) as total_sales,
            AVG(performance_score) as avg_performance
        FROM store_statistics
        GROUP BY region
        ORDER BY store_count DESC
        ''')

        stats = [dict(stat) for stat in cursor.fetchall()]
        return stats

    @staticmethod
    def get_store_stats_by_type():
        """الحصول على إحصائيات المتاجر مجمعة حسب النوع"""
        db = get_db()
        cursor = db.cursor()

        cursor.execute('''
        SELECT
            s.type as store_type,
            COUNT(DISTINCT ss.store_id) as store_count,
            SUM(ss.visits) as total_visits,
            SUM(ss.sales) as total_sales,
            AVG(ss.performance_score) as avg_performance
        FROM store_statistics ss
        JOIN stores s ON ss.store_id = s.id
        GROUP BY s.type
        ORDER BY store_count DESC
        ''')

        stats = [dict(stat) for stat in cursor.fetchall()]
        return stats

    @staticmethod
    def analyze_by_type(filters=None):
        """تحليل المتاجر حسب النوع"""
        db = get_db()
        cursor = db.cursor()

        # بناء استعلام SQL للحصول على بيانات المتاجر
        query = '''
        SELECT
            type,
            COUNT(*) as store_count
        FROM stores
        '''

        params = []
        where_clauses = []

        # إضافة شروط التصفية
        if filters:
            # تصفية حسب نوع المتجر
            if 'store_type' in filters and filters['store_type']:
                where_clauses.append("type = ?")
                params.append(filters['store_type'])

        # إضافة شروط WHERE إذا كانت موجودة
        if where_clauses:
            query += ' WHERE ' + ' AND '.join(where_clauses)

        # إضافة تجميع حسب النوع
        query += ' GROUP BY type ORDER BY store_count DESC'

        # تنفيذ الاستعلام
        cursor.execute(query, params)
        stats = [dict(stat) for stat in cursor.fetchall()]

        # إذا لم تكن هناك نتائج، إرجاع قائمة فارغة
        if not stats:
            return []

        # حساب النسب المئوية
        total_stores = sum(stat['store_count'] for stat in stats)
        for stat in stats:
            stat['percentage'] = (stat['store_count'] / total_stores) * 100 if total_stores > 0 else 0

        return stats

    @staticmethod
    def get_store_stats_by_time(_=None):
        """الحصول على إحصائيات المتاجر مجمعة حسب الوقت (يومي، أسبوعي، شهري)"""
        # تم إزالة هذه الوظيفة لأنها غير مستخدمة في التطبيق
        return []

# تم حذف دالة detect_anomalies لأنها غير مستخدمة

    @staticmethod
    def get_stores_by_region_and_city(city=None, district=None, include_performance=False):
        """الحصول على إحصائيات المتاجر حسب المناطق والمدن (دالة موحدة)"""
        db = get_db()
        cursor = db.cursor()

        # استعلام للحصول على جميع المتاجر مع وصف العنوان
        cursor.execute('''
        SELECT
            id,
            name,
            type,
            address,
            IFNULL(performance_score, 0) as performance_score
        FROM stores
        ''')

        stores = [dict(store) for store in cursor.fetchall()]

        # التحقق من وجود بيانات
        if not stores:
            return []

        # تجميع المتاجر حسب المنطقة باستخدام وصف العنوان
        region_stats = {}

        for store in stores:
            # استخراج المنطقة من وصف العنوان
            address = store.get('address', '')
            region_name = address.split(',')[0] if address and ',' in address else address

            # إذا كان العنوان فارغًا، استخدم "غير محدد"
            if not region_name:
                region_name = "غير محدد"

            # تجاهل "المنطقة غير محددة" فقط
            if region_name == 'المنطقة غير محددة':
                continue

            # استخراج المدينة الرئيسية والمنطقة الفرعية من المنطقة
            if ' - ' in region_name:
                main_city = region_name.split(' - ')[0]
                sub_district = region_name
            else:
                main_city = region_name
                sub_district = region_name

            # تصفية حسب المدينة إذا تم تحديدها
            if city and main_city != city:
                continue

            # تصفية حسب المنطقة الفرعية إذا تم تحديدها
            if district and district != region_name:
                continue

            # إضافة المنطقة إلى الإحصائيات
            if region_name not in region_stats:
                region_stats[region_name] = {
                    'region_name': region_name,
                    'store_count': 0,
                    'store_types': set(),
                    'main_city': main_city
                }
                if include_performance:
                    region_stats[region_name]['performance_scores'] = []

            region_stats[region_name]['store_count'] += 1
            region_stats[region_name]['store_types'].add(store['type'])
            if include_performance:
                region_stats[region_name]['performance_scores'].append(store['performance_score'])

        # تحويل الإحصائيات إلى قائمة وتنسيق البيانات
        stats = []
        for region_name, data in region_stats.items():
            stat_item = {
                'region_name': data['region_name'],
                'store_count': data['store_count'],
                'store_types': ','.join(data['store_types']),
                'main_city': data['main_city']
            }

            if include_performance and 'performance_scores' in data:
                avg_performance = sum(data['performance_scores']) / len(data['performance_scores']) if data['performance_scores'] else 0
                stat_item['avg_performance'] = avg_performance

            stats.append(stat_item)

        # ترتيب الإحصائيات حسب عدد المتاجر (تنازلياً) ثم حسب اسم المنطقة
        stats.sort(key=lambda x: (-x['store_count'], x['region_name']))

        return stats

    @staticmethod
    def get_stores_by_city_and_district(city=None, district=None):
        """الحصول على إحصائيات المتاجر حسب المدينة والمنطقة الفرعية (استخدام الدالة الموحدة)"""
        return StoreStatistics.get_stores_by_region_and_city(city=city, district=district, include_performance=True)

    @staticmethod
    def get_cities_and_districts():
        """الحصول على قائمة المدن والمناطق الفرعية المتاحة"""
        db = get_db()
        cursor = db.cursor()

        # استعلام للحصول على جميع المتاجر مع حقول عنوان المتجر الصحيحة
        cursor.execute('''
        SELECT city_name, region_name, address FROM stores
        WHERE (city_name IS NOT NULL AND city_name != '')
           OR (region_name IS NOT NULL AND region_name != '')
           OR (address IS NOT NULL AND address != '')
        ''')

        stores = [dict(store) for store in cursor.fetchall()]

        # التحقق من وجود بيانات
        if not stores:
            # إرجاع القيم الافتراضية إذا لم تكن هناك بيانات
            return {
                "cities": ["طرابلس", "بنغازي", "مصراتة"],
                "districts": {
                    "طرابلس": [
                        "طرابلس - وسط المدينة", "طرابلس - باب البحر", "طرابلس - سوق الجمعة",
                        "طرابلس - أبو سليم", "طرابلس - عين زارة", "طرابلس - تاجوراء",
                        "طرابلس - جنزور", "طرابلس - قرقارش", "طرابلس - حي الأندلس"
                    ],
                    "بنغازي": [
                        "بنغازي - وسط المدينة", "بنغازي - الليثي", "بنغازي - بوهديمة",
                        "بنغازي - الفويهات", "بنغازي - السلماني", "بنغازي - بنينا",
                        "بنغازي - القوارشة", "بنغازي - سيدي حسين", "بنغازي - الصابري"
                    ],
                    "مصراتة": [
                        "مصراتة - وسط المدينة", "مصراتة - الدافنية", "مصراتة - زاوية المحجوب",
                        "مصراتة - قصر أحمد", "مصراتة - طمينة"
                    ]
                }
            }

        # تجميع المدن والمناطق الفرعية من حقول عنوان المتجر الصحيحة
        cities = set()
        districts = {}

        for store in stores:
            city_name = store.get('city_name', '').strip() if store.get('city_name') else ''
            region_name = store.get('region_name', '').strip() if store.get('region_name') else ''
            address = store.get('address', '').strip() if store.get('address') else ''

            # أولوية الاستخراج: city_name و region_name أولاً، ثم address كبديل
            main_city = None
            sub_region = None

            # تجاهل "المنطقة غير محددة" فقط من region_name
            if region_name == 'المنطقة غير محددة':
                region_name = ''

            if city_name:
                main_city = city_name
                if region_name:
                    sub_region = region_name
            elif address:
                # إذا لم تكن هناك حقول city_name/region_name، استخدم address كبديل
                if ' - ' in address:
                    parts = address.split(' - ')
                    if len(parts) >= 2:
                        main_city = parts[0].strip()
                        sub_region = parts[1].strip()
                else:
                    main_city = address

            # إضافة المدينة الرئيسية إذا وجدت
            if main_city:
                cities.add(main_city)

                # إضافة المنطقة الفرعية إذا وجدت
                if main_city not in districts:
                    districts[main_city] = set()

                if sub_region:
                    districts[main_city].add(sub_region)

        # تحويل المجموعات إلى قوائم مرتبة
        cities_list = sorted(list(cities))
        districts_dict = {}

        for city in cities_list:
            if city in districts:
                districts_dict[city] = sorted(list(districts[city]))
            else:
                districts_dict[city] = []

            # إذا لم تكن هناك مناطق فرعية لهذه المدينة، استخدم القيم الافتراضية
            if not districts_dict[city]:
                if city == "طرابلس":
                    districts_dict[city] = [
                        "طرابلس - وسط المدينة", "طرابلس - باب البحر", "طرابلس - سوق الجمعة",
                        "طرابلس - أبو سليم", "طرابلس - عين زارة", "طرابلس - تاجوراء",
                        "طرابلس - جنزور", "طرابلس - قرقارش", "طرابلس - حي الأندلس"
                    ]
                elif city == "بنغازي":
                    districts_dict[city] = [
                        "بنغازي - وسط المدينة", "بنغازي - الليثي", "بنغازي - بوهديمة",
                        "بنغازي - الفويهات", "بنغازي - السلماني", "بنغازي - بنينا",
                        "بنغازي - القوارشة", "بنغازي - سيدي حسين", "بنغازي - الصابري"
                    ]

        return {"cities": cities_list, "districts": districts_dict}

    @staticmethod
    def generate_report(filters=None, format='json'):
        """إنشاء تقرير إحصائي باستخدام بيانات حقيقية من قاعدة البيانات"""
        db = get_db()
        cursor = db.cursor()

        # بناء استعلام SQL للحصول على بيانات المتاجر
        query = '''
        SELECT
            s.id as store_id,
            s.name as store_name,
            s.type as store_type,
            s.latitude,
            s.longitude,
            IFNULL(s.performance_score, 0) as performance_score
        FROM stores s
        '''

        params = []
        where_clauses = []

        # إضافة شروط التصفية
        if filters:
            # تصفية حسب نوع المتجر
            if 'store_type' in filters and filters['store_type']:
                where_clauses.append("s.type = ?")
                params.append(filters['store_type'])

        # إضافة شروط WHERE إذا كانت موجودة
        if where_clauses:
            query += ' WHERE ' + ' AND '.join(where_clauses)

        # تنفيذ الاستعلام
        cursor.execute(query, params)
        raw_stats = [dict(stat) for stat in cursor.fetchall()]

        # التحقق من وجود بيانات
        if not raw_stats:
            # إرجاع تقرير فارغ إذا لم تكن هناك بيانات
            empty_report = {
                'generated_at': datetime.now().isoformat(),
                'filters': filters or {},
                'summary': {
                    'total_stores': 0,
                    'total_regions': 0,
                    'avg_performance': 0
                },
                'region_stats': {},
                'data': []
            }

            # تحويل التقرير الفارغ إلى التنسيق المطلوب
            if format == 'json':
                return empty_report
            elif format in ['csv', 'excel', 'pdf']:
                return f"لا توجد بيانات متاحة للمعايير المحددة. تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            return empty_report

        # إضافة المنطقة لكل متجر باستخدام الدمج بين إحداثيات الخريطة ووصف العنوان
        stats = []
        for stat in raw_stats:
            # التحقق من وجود إحداثيات صالحة
            if not stat['latitude'] or not stat['longitude'] or \
               math.isnan(float(stat['latitude'])) or math.isnan(float(stat['longitude'])):
                continue

            # استخراج المنطقة من الإحداثيات (تم تبسيطها)
            map_region = "غير محدد"
            lat = float(stat['latitude'])
            lng = float(stat['longitude'])

            # تحديد المنطقة الرئيسية بناءً على الإحداثيات التقريبية
            if 32.7 <= lat <= 33.0 and 12.9 <= lng <= 13.4:
                map_region = "طرابلس"
            elif 31.9 <= lat <= 32.3 and 19.8 <= lng <= 20.3:
                map_region = "بنغازي"
            elif 32.2 <= lat <= 32.5 and 14.9 <= lng <= 15.3:
                map_region = "مصراتة"

            # إضافة المنطقة والمدينة الرئيسية إلى بيانات المتجر
            stat_with_region = stat.copy()

            # استخراج المنطقة من وصف العنوان باستخدام النظام القديم
            address_region = None
            if 'address' in stat and stat['address']:
                # استخدام النظام القديم
                clean_address = stat['address'].strip()
                address_parts = clean_address.split(' ')
                if address_parts:
                    address_region = address_parts[0]
                    # إذا كانت الكلمة الأولى قصيرة جداً، استخدم الكلمتين الأوليتين
                    if len(address_region) < 3 and len(address_parts) > 1:
                        address_region = f"{address_parts[0]} {address_parts[1]}"

            # تحديد المنطقة النهائية بناءً على الأولويات:
            # 1. إذا كان هناك وصف عنوان، استخدمه أولاً
            # 2. إذا لم يكن هناك وصف عنوان، استخدم المنطقة من الخريطة
            final_region = address_region if address_region else map_region

            # تخزين كلا المصدرين للمنطقة للاستخدام في التقارير
            stat_with_region['map_region'] = map_region
            stat_with_region['address_region'] = address_region
            stat_with_region['region'] = final_region

            # استخراج المدينة الرئيسية من المنطقة
            if ' - ' in final_region:
                main_city = final_region.split(' - ')[0]
            else:
                main_city = final_region

            stat_with_region['main_city'] = main_city
            stat_with_region['address'] = final_region  # استخدام المنطقة النهائية كعنوان

            stats.append(stat_with_region)

        # تجميع البيانات حسب المنطقة
        region_stats = {}
        for stat in stats:
            region = stat['region']
            if region not in region_stats:
                region_stats[region] = {
                    'store_count': 0,
                    'performance_scores': []
                }

            region_stats[region]['store_count'] += 1
            region_stats[region]['performance_scores'].append(stat['performance_score'])

        # حساب متوسط الأداء لكل منطقة
        for region in region_stats:
            scores = region_stats[region]['performance_scores']
            region_stats[region]['avg_performance'] = sum(scores) / len(scores) if scores else 0
            del region_stats[region]['performance_scores']  # حذف القائمة المؤقتة

        # إنشاء التقرير
        report = {
            'generated_at': datetime.now().isoformat(),
            'filters': filters or {},
            'summary': {
                'total_stores': len(stats),
                'total_regions': len(region_stats),
                'avg_performance': sum(stat['performance_score'] for stat in stats) / len(stats) if stats else 0
            },
            'region_stats': region_stats,
            'data': stats
        }

        # تحويل التقرير إلى التنسيق المطلوب
        if format == 'json':
            return report
        elif format == 'csv':
            # تنفيذ تحويل إلى CSV
            import csv
            import io

            output = io.StringIO()
            writer = csv.writer(output)

            # كتابة عنوان التقرير
            writer.writerow(['تقرير إحصائيات المتاجر'])
            writer.writerow(['تاريخ التقرير', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])

            # كتابة معلومات التصفية
            if filters:
                filter_info = []
                if 'region' in filters and filters['region']:
                    filter_info.append(f"المنطقة: {filters['region']}")
                if 'city' in filters and filters['city']:
                    filter_info.append(f"المدينة: {filters['city']}")
                if 'store_type' in filters and filters['store_type']:
                    filter_info.append(f"نوع المتجر: {filters['store_type']}")

                if filter_info:
                    writer.writerow(['معايير التصفية', ' | '.join(filter_info)])

            writer.writerow([])  # سطر فارغ

            # كتابة ملخص التقرير
            writer.writerow(['ملخص التقرير'])
            writer.writerow(['إجمالي المتاجر', report['summary']['total_stores']])
            writer.writerow(['إجمالي المناطق', report['summary']['total_regions']])

            # تحديد المنطقة ذات أكبر وأقل عدد من المتاجر
            max_region = {'name': 'غير متوفر', 'count': 0}
            min_region = {'name': 'غير متوفر', 'count': float('inf')}

            for region, stats in region_stats.items():
                if region and stats['store_count'] > max_region['count']:
                    max_region = {'name': region, 'count': stats['store_count']}
                if region and stats['store_count'] < min_region['count'] and stats['store_count'] > 0:
                    min_region = {'name': region, 'count': stats['store_count']}

            if min_region['count'] == float('inf'):
                min_region = {'name': 'غير متوفر', 'count': 0}

            writer.writerow(['المنطقة الأكثر متاجر', f"{max_region['name']} ({max_region['count']} متجر)"])
            writer.writerow(['المنطقة الأقل متاجر', f"{min_region['name']} ({min_region['count']} متجر)"])

            writer.writerow([])  # سطر فارغ

            # كتابة إحصائيات المناطق
            writer.writerow(['إحصائيات المناطق'])
            writer.writerow(['المنطقة', 'عدد المتاجر', 'النسبة المئوية', 'متوسط الأداء'])

            # ترتيب المناطق حسب عدد المتاجر
            sorted_regions = sorted(region_stats.items(), key=lambda x: x[1]['store_count'], reverse=True)

            for region, stats in sorted_regions:
                if not region:  # تخطي المناطق الفارغة
                    continue
                percentage = stats['store_count'] / report['summary']['total_stores'] if report['summary']['total_stores'] > 0 else 0
                writer.writerow([
                    region,
                    stats['store_count'],
                    f"{percentage:.1%}",
                    f"{stats.get('avg_performance', 0):.1f}"
                ])

            writer.writerow([])  # سطر فارغ

            # كتابة قائمة المتاجر
            writer.writerow(['قائمة المتاجر'])
            writer.writerow(['#', 'اسم المتجر', 'المنطقة', 'نوع المتجر', 'العنوان', 'درجة الأداء'])

            for i, stat in enumerate(stats, 1):
                writer.writerow([
                    i,
                    stat['store_name'],
                    stat['region'] or 'غير محدد',
                    stat.get('store_type', 'غير محدد'),
                    stat.get('address', 'غير محدد'),
                    f"{stat.get('performance_score', 0):.1f}"
                ])

            return output.getvalue()
        elif format == 'excel':
            # تنفيذ تحويل إلى Excel
            try:
                import io
                import xlsxwriter

                # إنشاء ملف Excel في الذاكرة
                output = io.BytesIO()
                workbook = xlsxwriter.Workbook(output, {'rtl_mode': True})  # تمكين وضع RTL للغة العربية

                # تعريف التنسيقات المشتركة
                header_format = workbook.add_format({
                    'bold': True,
                    'align': 'center',
                    'valign': 'vcenter',
                    'bg_color': '#d50000',
                    'font_color': 'white',
                    'border': 1,
                    'font_size': 12,
                    'text_wrap': True
                })

                # Formato eliminado porque no se utiliza

                data_format = workbook.add_format({
                    'align': 'right',
                    'valign': 'vcenter',
                    'border': 1,
                    'font_size': 10
                })

                number_format = workbook.add_format({
                    'align': 'center',
                    'valign': 'vcenter',
                    'border': 1,
                    'font_size': 10,
                    'num_format': '0'
                })

                percent_format = workbook.add_format({
                    'align': 'center',
                    'valign': 'vcenter',
                    'border': 1,
                    'font_size': 10,
                    'num_format': '0.0%',
                    'bg_color': '#f0f0f0'  # إضافة لون خلفية للنسب المئوية
                })

                title_format = workbook.add_format({
                    'bold': True,
                    'align': 'center',
                    'valign': 'vcenter',
                    'font_size': 16,
                    'color': '#d50000',
                    'border_bottom': 2,
                    'bottom_color': '#d50000'
                })

                subtitle_format = workbook.add_format({
                    'bold': True,
                    'align': 'center',
                    'valign': 'vcenter',
                    'font_size': 12,
                    'color': '#666666',
                    'italic': True
                })

                section_format = workbook.add_format({
                    'bold': True,
                    'align': 'right',
                    'valign': 'vcenter',
                    'font_size': 14,
                    'color': '#d50000',
                    'bg_color': '#f9f9f9',
                    'border_bottom': 1,
                    'bottom_color': '#d50000'
                })

                # ===== 1. إنشاء ورقة الملخص =====
                summary_sheet = workbook.add_worksheet('ملخص التقرير')

                # إضافة شعار وعنوان التقرير
                # إضافة صورة شعار Loacker إذا كانت متوفرة
                try:
                    logo_path = os.path.join('static', 'images', 'loacker-logo.svg')
                    if os.path.exists(logo_path):
                        summary_sheet.insert_image('A1', logo_path, {'x_scale': 0.5, 'y_scale': 0.5, 'x_offset': 10, 'y_offset': 5})
                except Exception as e:
                    print(f"Error inserting logo: {str(e)}")

                summary_sheet.merge_range('A1:F1', 'تقرير إحصائيات المتاجر', title_format)

                # إضافة تاريخ التقرير والوقت
                now = datetime.now()
                date_str = now.strftime('%Y-%m-%d')
                time_str = now.strftime('%H:%M:%S')
                summary_sheet.merge_range('A2:F2', f'تم إنشاء التقرير بتاريخ {date_str} الساعة {time_str}', subtitle_format)

                # إضافة معلومات التصفية
                filter_info = []
                if filters:
                    if 'region' in filters:
                        filter_info.append(f"المنطقة: {filters['region']}")
                    if 'store_type' in filters:
                        filter_info.append(f"نوع المتجر: {filters['store_type']}")

                if filter_info:
                    filter_text = " | ".join(filter_info)
                    summary_sheet.merge_range('A3:F3', f'معايير التصفية: {filter_text}', subtitle_format)

                # إضافة قسم الإحصائيات الرئيسية
                summary_sheet.merge_range('A5:F5', 'الإحصائيات الرئيسية', section_format)

                # إنشاء جدول الإحصائيات الرئيسية
                summary_data = [
                    ['إجمالي المتاجر', report['summary']['total_stores']],
                    ['عدد المناطق', report['summary']['total_regions']],
                    ['متوسط المتاجر لكل منطقة', report['summary']['total_stores'] / report['summary']['total_regions'] if report['summary']['total_regions'] > 0 else 0]
                ]

                # تحديد المنطقة ذات أكبر وأقل عدد من المتاجر
                max_region = {'name': 'غير متوفر', 'count': 0}
                min_region = {'name': 'غير متوفر', 'count': float('inf')}

                for region, stats in region_stats.items():
                    if region and stats['store_count'] > max_region['count']:
                        max_region = {'name': region, 'count': stats['store_count']}
                    if region and stats['store_count'] < min_region['count'] and stats['store_count'] > 0:
                        min_region = {'name': region, 'count': stats['store_count']}

                if min_region['count'] == float('inf'):
                    min_region = {'name': 'غير متوفر', 'count': 0}

                summary_data.extend([
                    ['المنطقة الأكثر متاجر', f"{max_region['name']} ({max_region['count']} متجر)"],
                    ['المنطقة الأقل متاجر', f"{min_region['name']} ({min_region['count']} متجر)"]
                ])

                # كتابة جدول الإحصائيات
                summary_headers = ['البيان', 'القيمة']
                for col, header in enumerate(summary_headers):
                    summary_sheet.write(6, col, header, header_format)

                for row, (label, value) in enumerate(summary_data, 7):
                    summary_sheet.write(row, 0, label, data_format)
                    if isinstance(value, (int, float)):
                        if isinstance(value, float) and not value.is_integer():
                            summary_sheet.write(row, 1, value, workbook.add_format({
                                'align': 'center',
                                'valign': 'vcenter',
                                'border': 1,
                                'font_size': 10,
                                'num_format': '0.0'
                            }))
                        else:
                            summary_sheet.write(row, 1, value, number_format)
                    else:
                        summary_sheet.write(row, 1, value, data_format)

                # تعديل عرض الأعمدة
                summary_sheet.set_column(0, 0, 25)
                summary_sheet.set_column(1, 1, 30)

                # إضافة قسم توزيع المتاجر حسب المنطقة
                summary_sheet.merge_range('A13:F13', 'توزيع المتاجر حسب المنطقة', section_format)

                # كتابة عناوين جدول المناطق
                region_headers = ['المنطقة', 'عدد المتاجر', 'النسبة المئوية']
                for col, header in enumerate(region_headers):
                    summary_sheet.write(14, col, header, header_format)

                # كتابة بيانات المناطق
                sorted_regions = sorted(region_stats.items(), key=lambda x: x[1]['store_count'], reverse=True)
                for row, (region, stats) in enumerate(sorted_regions, 15):
                    if not region:  # تخطي المناطق الفارغة
                        continue
                    summary_sheet.write(row, 0, region, data_format)
                    summary_sheet.write(row, 1, stats['store_count'], number_format)
                    percentage = stats['store_count'] / report['summary']['total_stores'] if report['summary']['total_stores'] > 0 else 0
                    summary_sheet.write(row, 2, percentage, percent_format)

                # ===== 2. إنشاء ورقة قائمة المتاجر =====
                stores_sheet = workbook.add_worksheet('قائمة المتاجر')

                # عنوان الورقة
                # إضافة صورة شعار Loacker إذا كانت متوفرة
                try:
                    logo_path = os.path.join('static', 'images', 'loacker-logo.svg')
                    if os.path.exists(logo_path):
                        stores_sheet.insert_image('A1', logo_path, {'x_scale': 0.5, 'y_scale': 0.5, 'x_offset': 10, 'y_offset': 5})
                except Exception as e:
                    print(f"Error inserting logo: {str(e)}")

                stores_sheet.merge_range('A1:F1', 'قائمة المتاجر', title_format)

                # كتابة عناوين جدول المتاجر
                store_headers = ['#', 'اسم المتجر', 'المنطقة', 'نوع المتجر', 'العنوان', 'درجة الأداء']
                for col, header in enumerate(store_headers):
                    stores_sheet.write(2, col, header, header_format)

                # كتابة بيانات المتاجر
                for row, stat in enumerate(stats, 3):
                    stores_sheet.write(row, 0, row - 2, number_format)  # رقم تسلسلي
                    stores_sheet.write(row, 1, stat['store_name'], data_format)

                    # استخدام المنطقة المستخرجة من الوصف والخريطة معاً
                    region_display = stat['region'] or 'غير محدد'

                    # تحميل المنطقة من قاعدة البيانات (من عنوان الوصف أو الخريطة)
                    if stat.get('map_region') and stat.get('address_region'):
                        # إذا كانت المنطقة من الخريطة مختلفة عن المنطقة من الوصف، نفضل المنطقة من الوصف
                        if stat['map_region'] != stat['address_region']:
                            region_display = stat['address_region']
                        else:
                            region_display = stat['map_region']
                    elif stat.get('map_region'):
                        region_display = stat['map_region']
                    elif stat.get('address_region'):
                        region_display = stat['address_region']

                    stores_sheet.write(row, 2, region_display, data_format)
                    stores_sheet.write(row, 3, stat.get('store_type', 'غير محدد'), data_format)
                    stores_sheet.write(row, 4, stat.get('address', 'غير محدد'), data_format)

                    # تنسيق درجة الأداء بألوان مختلفة حسب القيمة
                    performance = stat.get('performance_score', 0)
                    if performance >= 80:
                        perf_format = workbook.add_format({
                            'align': 'center', 'valign': 'vcenter', 'border': 1,
                            'bg_color': '#c8e6c9', 'font_size': 10, 'num_format': '0.0'
                        })
                    elif performance >= 60:
                        perf_format = workbook.add_format({
                            'align': 'center', 'valign': 'vcenter', 'border': 1,
                            'bg_color': '#fff9c4', 'font_size': 10, 'num_format': '0.0'
                        })
                    else:
                        perf_format = workbook.add_format({
                            'align': 'center', 'valign': 'vcenter', 'border': 1,
                            'bg_color': '#ffcdd2', 'font_size': 10, 'num_format': '0.0'
                        })

                    stores_sheet.write(row, 5, performance, perf_format)

                # تعديل عرض الأعمدة
                stores_sheet.set_column(0, 0, 5)   # #
                stores_sheet.set_column(1, 1, 25)  # اسم المتجر
                stores_sheet.set_column(2, 2, 20)  # المنطقة
                stores_sheet.set_column(3, 3, 15)  # نوع المتجر
                stores_sheet.set_column(4, 4, 30)  # العنوان
                stores_sheet.set_column(5, 5, 12)  # درجة الأداء

                # ===== 3. إنشاء ورقة إحصائيات المناطق =====
                region_sheet = workbook.add_worksheet('إحصائيات المناطق')

                # عنوان الورقة
                # إضافة صورة شعار Loacker إذا كانت متوفرة
                try:
                    logo_path = os.path.join('static', 'images', 'loacker-logo.svg')
                    if os.path.exists(logo_path):
                        region_sheet.insert_image('A1', logo_path, {'x_scale': 0.5, 'y_scale': 0.5, 'x_offset': 10, 'y_offset': 5})
                except Exception as e:
                    print(f"Error inserting logo: {str(e)}")

                region_sheet.merge_range('A1:E1', 'إحصائيات المتاجر حسب المنطقة', title_format)

                # كتابة عناوين جدول المناطق
                region_headers = ['#', 'المنطقة', 'عدد المتاجر', 'النسبة المئوية', 'متوسط الأداء']
                for col, header in enumerate(region_headers):
                    region_sheet.write(2, col, header, header_format)

                # كتابة بيانات المناطق
                for row, (region, stats) in enumerate(sorted_regions, 3):
                    if not region:  # تخطي المناطق الفارغة
                        continue
                    region_sheet.write(row, 0, row - 2, number_format)  # رقم تسلسلي
                    region_sheet.write(row, 1, region, data_format)
                    region_sheet.write(row, 2, stats['store_count'], number_format)
                    percentage = stats['store_count'] / report['summary']['total_stores'] if report['summary']['total_stores'] > 0 else 0
                    region_sheet.write(row, 3, percentage, percent_format)
                    region_sheet.write(row, 4, stats.get('avg_performance', 0), workbook.add_format({
                        'align': 'center', 'valign': 'vcenter', 'border': 1,
                        'font_size': 10, 'num_format': '0.0'
                    }))

                # تعديل عرض الأعمدة
                region_sheet.set_column(0, 0, 5)   # #
                region_sheet.set_column(1, 1, 30)  # المنطقة
                region_sheet.set_column(2, 2, 15)  # عدد المتاجر
                region_sheet.set_column(3, 3, 15)  # النسبة المئوية
                region_sheet.set_column(4, 4, 15)  # متوسط الأداء

                # ===== 4. إنشاء ورقة إحصائيات أنواع المتاجر =====
                type_sheet = workbook.add_worksheet('أنواع المتاجر')

                # عنوان الورقة
                # إضافة صورة شعار Loacker إذا كانت متوفرة
                try:
                    logo_path = os.path.join('static', 'images', 'loacker-logo.svg')
                    if os.path.exists(logo_path):
                        type_sheet.insert_image('A1', logo_path, {'x_scale': 0.5, 'y_scale': 0.5, 'x_offset': 10, 'y_offset': 5})
                except Exception as e:
                    print(f"Error inserting logo: {str(e)}")

                type_sheet.merge_range('A1:E1', 'إحصائيات المتاجر حسب النوع', title_format)

                # تجميع البيانات حسب نوع المتجر
                type_stats = {}
                for stat in stats:
                    store_type = stat.get('store_type', 'غير محدد')
                    if store_type not in type_stats:
                        type_stats[store_type] = {
                            'store_count': 0,
                            'performance_scores': []
                        }

                    type_stats[store_type]['store_count'] += 1
                    type_stats[store_type]['performance_scores'].append(stat.get('performance_score', 0))

                # حساب متوسط الأداء لكل نوع
                for store_type in type_stats:
                    scores = type_stats[store_type]['performance_scores']
                    type_stats[store_type]['avg_performance'] = sum(scores) / len(scores) if scores else 0
                    del type_stats[store_type]['performance_scores']  # حذف القائمة المؤقتة

                # كتابة عناوين جدول أنواع المتاجر
                type_headers = ['#', 'نوع المتجر', 'عدد المتاجر', 'النسبة المئوية', 'متوسط الأداء']
                for col, header in enumerate(type_headers):
                    type_sheet.write(2, col, header, header_format)

                # كتابة بيانات أنواع المتاجر
                sorted_types = sorted(type_stats.items(), key=lambda x: x[1]['store_count'], reverse=True)
                for row, (store_type, stats) in enumerate(sorted_types, 3):
                    type_sheet.write(row, 0, row - 2, number_format)  # رقم تسلسلي
                    type_sheet.write(row, 1, store_type, data_format)
                    type_sheet.write(row, 2, stats['store_count'], number_format)
                    percentage = stats['store_count'] / report['summary']['total_stores'] if report['summary']['total_stores'] > 0 else 0
                    type_sheet.write(row, 3, percentage, percent_format)
                    type_sheet.write(row, 4, stats.get('avg_performance', 0), workbook.add_format({
                        'align': 'center', 'valign': 'vcenter', 'border': 1,
                        'font_size': 10, 'num_format': '0.0'
                    }))

                # إغلاق المصنف وإرجاع البيانات
                workbook.close()
                output.seek(0)
                return output.getvalue()

            except ImportError:
                # إذا لم تكن مكتبة xlsxwriter متاحة، إرجاع CSV كبديل
                import csv
                import io

                output = io.StringIO()
                writer = csv.writer(output)

                # كتابة الرأس
                writer.writerow(['اسم المتجر', 'المنطقة', 'نوع المتجر'])

                # كتابة البيانات
                for stat in stats:
                    writer.writerow([
                        stat['store_name'],
                        stat['region'],
                        stat.get('store_type', 'غير محدد')
                    ])

                return output.getvalue()

        # التنسيق الافتراضي هو JSON
        return report

    @staticmethod
    def save_report(title, description, filters, format, user_id, scheduled=False, schedule_frequency=None):
        """حفظ تقرير في قاعدة البيانات"""
        now = datetime.now().isoformat()

        db = get_db()
        cursor = db.cursor()

        # تحويل filters إلى JSON
        import json
        filters_json = json.dumps(filters) if filters else '{}'

        cursor.execute('''
        INSERT INTO statistics_reports (
            title, description, filters, format, user_id,
            scheduled, schedule_frequency, last_generated, created_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            title,
            description,
            filters_json,
            format,
            user_id,
            1 if scheduled else 0,
            schedule_frequency,
            now,
            now
        ))
        db.commit()

        # الحصول على معرف التقرير الجديد
        cursor.execute('SELECT last_insert_rowid()')
        report_id = cursor.fetchone()[0]

        return report_id


class User(UserMixin):
    """نموذج المستخدم"""

    def __init__(self, id=None, username=None, email=None, phone=None, role_id=None, is_active=True):
        self.id = str(id) if id is not None else None
        self.username = username
        self.email = email
        self.phone = phone
        self.role_id = role_id
        self._is_active = is_active

    def get_id(self):
        return str(self.id)

    @property
    def is_active(self):
        return self._is_active

    @is_active.setter
    def is_active(self, value):
        self._is_active = value

    @staticmethod
    def get_all():
        """الحصول على جميع المستخدمين"""
        db = get_db()
        cursor = db.cursor()
        cursor.execute('SELECT * FROM users ORDER BY created_at DESC')
        users = [dict(user) for user in cursor.fetchall()]
        return users

    @staticmethod
    def get_marketers_for_list(list_id):
        """الحصول على المسوقين المخصصين لقائمة معينة"""
        db = get_db()
        cursor = db.cursor()

        # الحصول على المسوقين المخصصين لهذه القائمة
        cursor.execute('''
        SELECT u.id, u.username, u.phone, u.is_active
        FROM users u
        JOIN marketer_lists ml ON u.id = ml.user_id
        WHERE ml.list_id = ? AND u.role_id = 2
        ORDER BY u.username
        ''', (list_id,))

        marketers = [dict(marketer) for marketer in cursor.fetchall()]
        return marketers

    @staticmethod
    def get_marketers():
        """الحصول على جميع المسوقين"""
        db = get_db()
        cursor = db.cursor()

        # الحصول على جميع المسوقين النشطين
        cursor.execute('''
        SELECT id, username, phone, is_active
        FROM users
        WHERE role_id = 2 AND is_active = 1
        ORDER BY username
        ''')

        marketers = [dict(marketer) for marketer in cursor.fetchall()]
        return marketers

    @staticmethod
    def get_by_id(user_id):
        """الحصول على مستخدم بواسطة المعرف"""
        # طباعة معرف المستخدم للتشخيص
        print(f"Getting user with ID: {user_id}, type: {type(user_id)}")

        db = get_db()
        cursor = db.cursor()
        cursor.execute('SELECT * FROM users WHERE id = ?', (str(user_id),))
        user = cursor.fetchone()

        if user:
            user_dict = dict(user)
            print(f"Found user: {user_dict['username']}")
            return user_dict
        else:
            print(f"No user found with ID: {user_id}")
            return None

    @staticmethod
    def get_by_username(username):
        """الحصول على مستخدم بواسطة اسم المستخدم"""
        # طباعة معلومات البحث للتشخيص
        print(f"Getting user by username: {username}")

        db = get_db()
        cursor = db.cursor()
        cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
        user = cursor.fetchone()

        if user:
            user_dict = dict(user)
            print(f"Found user by username: id={user_dict.get('id')}, username={user_dict.get('username')}")
            return user_dict
        else:
            print(f"No user found with username: {username}")
            return None

    @staticmethod
    def get_by_phone(phone):
        """الحصول على مستخدم بواسطة رقم الهاتف"""
        db = get_db()
        cursor = db.cursor()
        cursor.execute('SELECT * FROM users WHERE phone = ?', (phone,))
        user = cursor.fetchone()
        return dict(user) if user else None

    @staticmethod
    def get_by_email(email):
        """الحصول على مستخدم بواسطة البريد الإلكتروني"""
        db = get_db()
        cursor = db.cursor()
        cursor.execute('SELECT * FROM users WHERE email = ?', (email,))
        user = cursor.fetchone()
        return dict(user) if user else None

    @staticmethod
    def create(data):
        """إنشاء مستخدم جديد"""
        now = datetime.now().isoformat()
        user_id = f"user-{uuid.uuid4().hex[:8]}"  # إنشاء معرف فريد بتنسيق مناسب

        # التحقق من وجود البيانات المطلوبة
        if not data.get('username'):
            raise ValidationError('يجب توفير اسم المستخدم')

        if not data.get('phone'):
            raise ValidationError('يجب توفير رقم الهاتف')

        if not data.get('password'):
            raise ValidationError('يجب توفير كلمة المرور')

        try:
            # التحقق من عدم وجود اسم المستخدم أو رقم الهاتف بالفعل
            db = get_db()
            cursor = db.cursor()

            # التحقق من اسم المستخدم
            cursor.execute('SELECT * FROM users WHERE username = ?', (data['username'],))
            if cursor.fetchone():
                raise ValidationError('اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر')

            # التحقق من رقم الهاتف
            cursor.execute('SELECT * FROM users WHERE phone = ?', (data['phone'],))
            if cursor.fetchone():
                raise ValidationError('رقم الهاتف موجود بالفعل، يرجى استخدام رقم آخر')

            # إنشاء هاش لكلمة المرور
            password_hash = generate_password_hash(data['password'])

            # طباعة معلومات المستخدم الجديد للتشخيص
            print(f"Creating new user: id={user_id}, username={data['username']}")

            # إدراج المستخدم الجديد
            cursor.execute('''
            INSERT INTO users (id, username, phone, email, password_hash, role_id, is_active, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_id,
                data['username'],
                data['phone'],
                data.get('email', f"{data['username']}@example.com"),  # قيمة افتراضية للبريد الإلكتروني
                password_hash,
                data.get('role_id', Role.VISITOR),  # الدور الافتراضي هو زائر
                data.get('is_active', 1),
                now,
                now
            ))
            db.commit()

            # التحقق من نجاح الإدراج
            cursor.execute('SELECT * FROM users WHERE id = ?', (user_id,))
            new_user = cursor.fetchone()
            if new_user:
                print(f"User created successfully: id={user_id}")
                # إرجاع المستخدم المنشأ
                return User.get_by_username(data['username'])
            else:
                print(f"WARNING: User creation may have failed for id={user_id}")
                raise ValidationError('حدث خطأ أثناء إنشاء المستخدم')

        except ValidationError:
            # إعادة رفع ValidationError كما هو
            raise
        except sqlite3.Error as e:
            print(f"SQLite error: {str(e)}")
            raise ValidationError(f'حدث خطأ في قاعدة البيانات: {str(e)}')
        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            raise ValidationError('حدث خطأ غير متوقع أثناء إنشاء المستخدم')

    @staticmethod
    def update(user_id, data):
        """تحديث مستخدم موجود"""
        now = datetime.now().isoformat()

        # الحصول على المستخدم الحالي
        current_user = User.get_by_id(user_id)
        if not current_user:
            return None

        # التحقق من عدم وجود اسم المستخدم أو البريد الإلكتروني لمستخدم آخر
        db = get_db()
        cursor = db.cursor()

        if 'username' in data and data['username'] != current_user['username']:
            cursor.execute('SELECT * FROM users WHERE username = ? AND id != ?', (data['username'], user_id))
            if cursor.fetchone():
                return None  # اسم المستخدم موجود بالفعل لمستخدم آخر

        if 'phone' in data and data['phone'] != current_user['phone']:
            cursor.execute('SELECT * FROM users WHERE phone = ? AND id != ?', (data['phone'], user_id))
            if cursor.fetchone():
                return None  # رقم الهاتف موجود بالفعل لمستخدم آخر

        # بناء استعلام التحديث
        update_fields = []
        params = []

        if 'username' in data:
            update_fields.append('username = ?')
            params.append(data['username'])

        if 'phone' in data:
            update_fields.append('phone = ?')
            params.append(data['phone'])

        if 'email' in data:
            update_fields.append('email = ?')
            params.append(data['email'])

        if 'password' in data:
            update_fields.append('password_hash = ?')
            params.append(generate_password_hash(data['password']))

        if 'role_id' in data:
            update_fields.append('role_id = ?')
            params.append(data['role_id'])

        if 'is_active' in data:
            update_fields.append('is_active = ?')
            params.append(1 if data['is_active'] else 0)

        update_fields.append('updated_at = ?')
        params.append(now)

        # إضافة معرف المستخدم إلى المعلمات
        params.append(user_id)

        # تنفيذ استعلام التحديث
        cursor.execute(f'''
        UPDATE users
        SET {', '.join(update_fields)}
        WHERE id = ?
        ''', params)
        db.commit()

        # إرجاع المستخدم المحدث
        return User.get_by_id(user_id)

    @staticmethod
    def delete(user_id):
        """حذف مستخدم"""
        # الحصول على المستخدم
        user = User.get_by_id(user_id)
        if not user:
            return False

        # حذف المستخدم من قاعدة البيانات
        db = get_db()
        cursor = db.cursor()
        cursor.execute('DELETE FROM users WHERE id = ?', (user_id,))
        db.commit()

        return True

    @staticmethod
    def verify_password(username, password):
        """التحقق من صحة كلمة المرور"""
        # طباعة معلومات التحقق للتشخيص
        print(f"Verifying password for username or phone: {username}")

        # التحقق من اسم المستخدم
        user = User.get_by_username(username)

        # إذا لم يتم العثور على المستخدم باسم المستخدم، نحاول البحث برقم الهاتف
        if not user:
            print(f"No user found with username: {username}, trying phone number...")
            user = User.get_by_phone(username)

            # إذا لم يتم العثور على المستخدم برقم الهاتف، نحاول البحث بالبريد الإلكتروني
            if not user:
                print(f"No user found with phone: {username}, trying email...")
                user = User.get_by_email(username)

        if not user:
            print(f"No user found with username, phone, or email: {username}")
            return None

        # طباعة معلومات المستخدم للتشخيص
        print(f"Found user: id={user['id']}, username={user['username']}")

        # التحقق من كلمة المرور
        password_check = check_password_hash(user['password_hash'], password)
        print(f"Password check result: {password_check}")

        if password_check:
            return user

        return None