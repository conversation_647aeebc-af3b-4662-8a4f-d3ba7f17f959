/**
 * SystemMonitor - نظام مراقبة وتشخيص التطبيق
 * يراقب أداء النظام ويكشف المشاكل
 * Created: 2024 - Monitoring System
 */
class SystemMonitor {
    static instance = null;
    
    static getInstance() {
        if (!this.instance) {
            this.instance = new SystemMonitor();
        }
        return this.instance;
    }
    
    constructor() {
        if (SystemMonitor.instance) {
            return SystemMonitor.instance;
        }
        
        // إعدادات المراقبة
        this.settings = {
            enableLogging: true,
            enablePerformanceMonitoring: true,
            enableErrorTracking: true,
            logLevel: 'info', // debug, info, warn, error
            maxLogEntries: 1000,
            reportInterval: 30000 // 30 ثانية
        };
        
        // سجلات النظام
        this.logs = [];
        this.errors = [];
        this.warnings = [];
        this.performanceMetrics = [];
        
        // حالة النظام
        this.systemStatus = {
            isHealthy: true,
            lastCheck: Date.now(),
            components: {
                deviceDetector: 'unknown',
                stateManager: 'unknown',
                apiManager: 'unknown',
                mapManager: 'unknown',
                performanceOptimizer: 'unknown',
                mobileApp: 'unknown'
            }
        };
        
        SystemMonitor.instance = this;
        this.init();
    }
    
    /**
     * تهيئة النظام
     */
    init() {
        console.log('🔍 System Monitor initializing...');
        
        // إعداد معالجة الأخطاء العامة
        this.setupErrorHandling();
        
        // بدء مراقبة الأداء
        if (this.settings.enablePerformanceMonitoring) {
            this.startPerformanceMonitoring();
        }
        
        // بدء فحص صحة النظام
        this.startHealthChecks();
        
        // إعداد التقارير الدورية
        this.setupPeriodicReporting();
        
        console.log('✅ System Monitor initialized');
    }
    
    /**
     * إعداد معالجة الأخطاء
     */
    setupErrorHandling() {
        // معالجة الأخطاء العامة
        window.addEventListener('error', (event) => {
            this.logError('JavaScript Error', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });
        
        // معالجة الأخطاء غير المعالجة في Promise
        window.addEventListener('unhandledrejection', (event) => {
            this.logError('Unhandled Promise Rejection', {
                reason: event.reason,
                promise: event.promise
            });
        });
        
        // معالجة أخطاء الشبكة
        window.addEventListener('offline', () => {
            this.logWarning('Network Status', 'Application went offline');
        });
        
        window.addEventListener('online', () => {
            this.logInfo('Network Status', 'Application came back online');
        });
    }
    
    /**
     * بدء مراقبة الأداء
     */
    startPerformanceMonitoring() {
        // مراقبة أوقات التحميل
        this.monitorLoadTimes();
        
        // مراقبة استخدام الذاكرة
        this.monitorMemoryUsage();
        
        // مراقبة FPS
        this.monitorFrameRate();
    }
    
    /**
     * مراقبة أوقات التحميل
     */
    monitorLoadTimes() {
        // مراقبة تحميل DOM
        document.addEventListener('DOMContentLoaded', () => {
            const loadTime = performance.now();
            this.recordMetric('dom_load_time', loadTime);
            this.logInfo('Performance', `DOM loaded in ${loadTime.toFixed(2)}ms`);
        });
        
        // مراقبة تحميل النافذة
        window.addEventListener('load', () => {
            const loadTime = performance.now();
            this.recordMetric('window_load_time', loadTime);
            this.logInfo('Performance', `Window loaded in ${loadTime.toFixed(2)}ms`);
            
            // تحليل Navigation Timing
            this.analyzeNavigationTiming();
        });
    }
    
    /**
     * تحليل Navigation Timing
     */
    analyzeNavigationTiming() {
        if ('performance' in window && 'getEntriesByType' in performance) {
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation) {
                const metrics = {
                    dns_lookup: navigation.domainLookupEnd - navigation.domainLookupStart,
                    tcp_connect: navigation.connectEnd - navigation.connectStart,
                    request_response: navigation.responseEnd - navigation.requestStart,
                    dom_processing: navigation.domContentLoadedEventEnd - navigation.responseEnd,
                    total_load: navigation.loadEventEnd - navigation.navigationStart
                };
                
                Object.entries(metrics).forEach(([key, value]) => {
                    this.recordMetric(key, value);
                });
                
                this.logInfo('Navigation Timing', metrics);
            }
        }
    }
    
    /**
     * مراقبة استخدام الذاكرة
     */
    monitorMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                const metrics = {
                    used: memory.usedJSHeapSize / 1024 / 1024, // MB
                    total: memory.totalJSHeapSize / 1024 / 1024, // MB
                    limit: memory.jsHeapSizeLimit / 1024 / 1024 // MB
                };
                
                this.recordMetric('memory_usage', metrics.used);
                
                // تحذير إذا كان الاستخدام مرتفعاً
                if (metrics.used / metrics.limit > 0.8) {
                    this.logWarning('Memory Usage', `High memory usage: ${metrics.used.toFixed(2)}MB`);
                }
            }, 10000); // كل 10 ثواني
        }
    }
    
    /**
     * مراقبة FPS
     */
    monitorFrameRate() {
        let lastTime = performance.now();
        let frameCount = 0;
        
        const measureFPS = () => {
            frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - lastTime >= 1000) {
                const fps = frameCount;
                this.recordMetric('fps', fps);
                
                // تحذير إذا كان FPS منخفضاً
                if (fps < 30) {
                    this.logWarning('Performance', `Low FPS detected: ${fps}`);
                }
                
                frameCount = 0;
                lastTime = currentTime;
            }
            
            requestAnimationFrame(measureFPS);
        };
        
        requestAnimationFrame(measureFPS);
    }
    
    /**
     * بدء فحص صحة النظام
     */
    startHealthChecks() {
        setInterval(() => {
            this.performHealthCheck();
        }, 15000); // كل 15 ثانية
    }
    
    /**
     * فحص صحة النظام
     */
    performHealthCheck() {
        const components = this.systemStatus.components;
        
        // فحص كاشف الأجهزة
        components.deviceDetector = window.unifiedDeviceDetector ? 'healthy' : 'missing';
        
        // فحص مدير الحالة
        components.stateManager = window.unifiedStateManager ? 'healthy' : 'missing';
        
        // فحص مدير API
        components.apiManager = window.unifiedAPIManager ? 'healthy' : 'missing';
        
        // فحص مدير الخرائط
        components.mapManager = window.unifiedMapManager ? 'healthy' : 'missing';
        
        // فحص محسن الأداء
        components.performanceOptimizer = window.performanceOptimizer ? 'healthy' : 'missing';
        
        // فحص تطبيق الهاتف
        components.mobileApp = window.mobileApp ? 'healthy' : 'missing';
        
        // تحديد الحالة العامة
        const healthyComponents = Object.values(components).filter(status => status === 'healthy').length;
        const totalComponents = Object.keys(components).length;
        
        this.systemStatus.isHealthy = healthyComponents >= totalComponents * 0.7; // 70% على الأقل
        this.systemStatus.lastCheck = Date.now();
        
        // تسجيل المشاكل
        Object.entries(components).forEach(([component, status]) => {
            if (status === 'missing') {
                this.logWarning('Health Check', `Component missing: ${component}`);
            }
        });
        
        this.logDebug('Health Check', `${healthyComponents}/${totalComponents} components healthy`);
    }
    
    /**
     * إعداد التقارير الدورية
     */
    setupPeriodicReporting() {
        setInterval(() => {
            this.generateReport();
        }, this.settings.reportInterval);
    }
    
    /**
     * تسجيل رسالة
     */
    log(level, category, message, data = null) {
        if (!this.settings.enableLogging) return;
        
        const logEntry = {
            timestamp: Date.now(),
            level: level,
            category: category,
            message: message,
            data: data
        };
        
        this.logs.push(logEntry);
        
        // الحفاظ على حجم السجل
        if (this.logs.length > this.settings.maxLogEntries) {
            this.logs.shift();
        }
        
        // تسجيل في وحدة التحكم
        const consoleMethod = console[level] || console.log;
        consoleMethod(`[${category}] ${message}`, data || '');
        
        // تصنيف الرسائل
        if (level === 'error') {
            this.errors.push(logEntry);
        } else if (level === 'warn') {
            this.warnings.push(logEntry);
        }
    }
    
    /**
     * تسجيل معلومات
     */
    logInfo(category, message, data = null) {
        this.log('info', category, message, data);
    }
    
    /**
     * تسجيل تحذير
     */
    logWarning(category, message, data = null) {
        this.log('warn', category, message, data);
    }
    
    /**
     * تسجيل خطأ
     */
    logError(category, message, data = null) {
        this.log('error', category, message, data);
    }
    
    /**
     * تسجيل تصحيح
     */
    logDebug(category, message, data = null) {
        if (this.settings.logLevel === 'debug') {
            this.log('debug', category, message, data);
        }
    }
    
    /**
     * تسجيل مقياس أداء
     */
    recordMetric(name, value) {
        this.performanceMetrics.push({
            timestamp: Date.now(),
            name: name,
            value: value
        });
        
        // الحفاظ على حجم المقاييس
        if (this.performanceMetrics.length > this.settings.maxLogEntries) {
            this.performanceMetrics.shift();
        }
    }
    
    /**
     * إنشاء تقرير
     */
    generateReport() {
        const report = {
            timestamp: Date.now(),
            systemStatus: this.systemStatus,
            summary: {
                totalLogs: this.logs.length,
                errors: this.errors.length,
                warnings: this.warnings.length,
                metrics: this.performanceMetrics.length
            },
            recentErrors: this.errors.slice(-5),
            recentWarnings: this.warnings.slice(-5),
            performanceSummary: this.getPerformanceSummary()
        };
        
        this.logInfo('System Report', 'Generated system report', report);
        return report;
    }
    
    /**
     * الحصول على ملخص الأداء
     */
    getPerformanceSummary() {
        const recent = this.performanceMetrics.slice(-100); // آخر 100 مقياس
        const summary = {};
        
        // تجميع المقاييس حسب النوع
        const grouped = recent.reduce((acc, metric) => {
            if (!acc[metric.name]) acc[metric.name] = [];
            acc[metric.name].push(metric.value);
            return acc;
        }, {});
        
        // حساب الإحصائيات
        Object.entries(grouped).forEach(([name, values]) => {
            summary[name] = {
                count: values.length,
                avg: values.reduce((a, b) => a + b, 0) / values.length,
                min: Math.min(...values),
                max: Math.max(...values),
                latest: values[values.length - 1]
            };
        });
        
        return summary;
    }
    
    /**
     * الحصول على حالة النظام
     */
    getSystemStatus() {
        return { ...this.systemStatus };
    }
    
    /**
     * الحصول على السجلات
     */
    getLogs(level = null, limit = 100) {
        let logs = this.logs;
        
        if (level) {
            logs = logs.filter(log => log.level === level);
        }
        
        return logs.slice(-limit);
    }
    
    /**
     * مسح السجلات
     */
    clearLogs() {
        this.logs = [];
        this.errors = [];
        this.warnings = [];
        this.performanceMetrics = [];
        this.logInfo('System Monitor', 'Logs cleared');
    }
    
    /**
     * تصدير البيانات
     */
    exportData() {
        return {
            logs: this.logs,
            errors: this.errors,
            warnings: this.warnings,
            metrics: this.performanceMetrics,
            systemStatus: this.systemStatus,
            settings: this.settings
        };
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.systemMonitor = SystemMonitor.getInstance();
    console.log('🔍 System Monitor ready');
});
