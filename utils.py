"""
ملف الوظائف المساعدة لتطبيق Loacker

يحتوي هذا الملف على وظائف مساعدة مختلفة تستخدم في أنحاء التطبيق
مثل معالجة الملفات وغيرها من الوظائف المشتركة.
"""

import os
import uuid
from flask import current_app, jsonify
from werkzeug.utils import secure_filename

def allowed_file(filename):
    """
    التحقق من أن الملف له امتداد مسموح به
    
    Args:
        filename (str): اسم الملف للتحقق منه
        
    Returns:
        bool: True إذا كان الملف مسموح به، False خلاف ذلك
    """
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def save_uploaded_file(file, folder=None, custom_filename=None):
    """
    حفظ ملف مرفوع في المجلد المحدد
    
    Args:
        file: كائن الملف المرفوع
        folder (str, optional): المجلد الفرعي داخل مجلد التحميلات. الافتراضي None.
        custom_filename (str, optional): اسم مخصص للملف. الافتراضي None.
        
    Returns:
        str: مسار الملف المحفوظ
    """
    if not file:
        return None
        
    # الحصول على مجلد التحميلات من إعدادات التطبيق
    upload_folder = current_app.config['UPLOAD_FOLDER']
    
    # إضافة المجلد الفرعي إذا تم تحديده
    if folder:
        target_folder = os.path.join(upload_folder, folder)
        # إنشاء المجلد إذا لم يكن موجودًا
        if not os.path.exists(target_folder):
            os.makedirs(target_folder)
    else:
        target_folder = upload_folder
    
    # تأمين اسم الملف
    if custom_filename:
        filename = custom_filename
    else:
        filename = secure_filename(file.filename)
    
    # حفظ الملف
    file_path = os.path.join(target_folder, filename)
    file.save(file_path)
    
    # إرجاع المسار النسبي للملف
    relative_path = os.path.join(os.path.relpath(target_folder, os.path.dirname(os.path.abspath(__file__))), filename)
    return relative_path

def delete_file(file_path):
    """
    حذف ملف من نظام الملفات
    
    Args:
        file_path (str): مسار الملف المراد حذفه
        
    Returns:
        bool: True إذا تم الحذف بنجاح، False خلاف ذلك
    """
    if not file_path:
        return False
    
    # التحقق من وجود الملف
    if os.path.exists(file_path):
        try:
            os.remove(file_path)
            return True
        except Exception:
            return False
    
    return False

def generate_unique_filename(original_filename):
    """
    إنشاء اسم ملف فريد باستخدام UUID

    Args:
        original_filename (str): اسم الملف الأصلي

    Returns:
        str: اسم الملف الفريد
    """
    if not original_filename:
        return None

    # الحصول على امتداد الملف
    file_extension = original_filename.rsplit('.', 1)[1].lower() if '.' in original_filename else ''

    # إنشاء اسم فريد
    unique_id = str(uuid.uuid4())

    # دمج الاسم الأصلي مع المعرف الفريد
    base_name = original_filename.rsplit('.', 1)[0] if '.' in original_filename else original_filename
    unique_filename = f"{unique_id}_{secure_filename(base_name)}"

    # إضافة الامتداد
    if file_extension:
        unique_filename += f".{file_extension}"

    return unique_filename

def create_error_response(message, status_code=400):
    """
    إنشاء استجابة خطأ موحدة

    Args:
        message (str): رسالة الخطأ
        status_code (int): رمز حالة HTTP

    Returns:
        tuple: استجابة JSON مع رمز الحالة
    """
    return jsonify({'error': message, 'success': False}), status_code

def create_success_response(data=None, message=None):
    """
    إنشاء استجابة نجاح موحدة

    Args:
        data: البيانات المراد إرجاعها
        message (str): رسالة النجاح (اختيارية)

    Returns:
        dict: استجابة JSON
    """
    response = {'success': True}

    if data is not None:
        response.update(data)

    if message:
        response['message'] = message

    return jsonify(response)

def validate_required_fields(form_data, required_fields):
    """
    التحقق من وجود الحقول المطلوبة

    Args:
        form_data: بيانات النموذج
        required_fields (dict): قاموس الحقول المطلوبة مع أسمائها العربية

    Returns:
        str or None: رسالة الخطأ إذا كان هناك حقل مفقود، None إذا كانت جميع الحقول موجودة
    """
    for field, arabic_name in required_fields.items():
        if not form_data.get(field):
            return f'{arabic_name} مطلوب'
    return None
