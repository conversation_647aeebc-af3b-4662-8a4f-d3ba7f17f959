# نظام إدارة متاجر Loacker

نظام ويب شامل لإدارة متاجر Loacker مع إمكانيات متقدمة لإدارة المستخدمين والمناطق والإحصائيات.

## المميزات الرئيسية

### 🏪 إدارة المتاجر
- إضافة وتعديل وحذف المتاجر
- رفع صور المتاجر
- تصنيف المتاجر حسب النوع (A, B, D)
- إدارة المتاجر المعلقة للمراجعة

### 👥 إدارة المستخدمين
- نظام تسجيل دخول آمن
- ثلاثة أدوار: مدير، مسوق، زائر
- إدارة الصلاحيات حسب الدور
- لوحة إدارة شاملة للمديرين

### 🗺️ إدارة المناطق
- نظام مناطق هرمي (رئيسية وفرعية)
- تغطية المدن الليبية الرئيسية: طرابلس، بنغازي، مصراتة
- استخراج ذكي للمواقع من النصوص
- إحداثيات جغرافية دقيقة

### 📊 الإحصائيات والتقارير
- إحصائيات شاملة للمتاجر
- تحليل الأداء حسب المناطق
- رسوم بيانية تفاعلية
- تصدير البيانات

### 🔒 الأمان
- حماية CSRF
- تشفير كلمات المرور
- نظام سجلات أمنية شامل
- إدارة الجلسات الآمنة

## متطلبات النظام

- Python 3.8+
- Flask 2.0+
- SQLite 3
- متصفح ويب حديث

## التثبيت والتشغيل

### 1. تحضير البيئة
```bash
# إنشاء بيئة افتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
# على Windows:
venv\Scripts\activate
# على Linux/Mac:
source venv/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt
```

### 2. تهيئة قاعدة البيانات
```bash
# تشغيل ملف تهيئة المناطق
python init_regions_unified.py
```

### 3. تشغيل التطبيق
```bash
# تشغيل الخادم
python app.py
```

سيكون التطبيق متاحاً على: `http://localhost:5000`

## هيكل المشروع

```
LOACKER برنامج/
├── app.py                    # التطبيق الرئيسي
├── config.py                 # إعدادات التطبيق
├── db.py                     # إدارة قاعدة البيانات
├── models.py                 # نماذج البيانات
├── forms.py                  # نماذج الويب
├── auth.py                   # نظام المصادقة
├── logger.py                 # نظام السجلات
├── utils.py                  # وظائف مساعدة
├── init_regions_unified.py   # تهيئة المناطق
├── init.py                   # تهيئة التطبيق المتقدمة
├── requirements.txt          # متطلبات Python
├── database.db              # قاعدة البيانات الرئيسية
├── stores.db                # قاعدة بيانات المتاجر
├── static/                  # الملفات الثابتة
│   ├── css/                 # ملفات التنسيق
│   ├── js/                  # ملفات JavaScript
│   ├── images/              # الصور
│   └── uploads/             # ملفات المتاجر المرفوعة
├── templates/               # قوالب HTML
│   ├── admin/               # قوالب الإدارة
│   └── errors/              # قوالب الأخطاء
├── routes/                  # مسارات إضافية
├── logs/                    # ملفات السجلات
└── venv/                    # البيئة الافتراضية
```

## الاستخدام

### تسجيل الدخول الأولي
- المدير الافتراضي: `admin` / `admin123`
- يمكن إنشاء مستخدمين جدد من لوحة الإدارة

### إضافة متجر جديد
1. انتقل إلى الصفحة الرئيسية
2. اضغط على "إضافة متجر"
3. املأ البيانات المطلوبة
4. ارفع صورة المتجر (اختياري)
5. اضغط "حفظ"

### إدارة المناطق
- يتم تحديد المناطق تلقائياً من وصف العنوان
- يمكن إضافة مناطق جديدة من لوحة الإدارة
- النظام يدعم المناطق الهرمية

## الصيانة والتطوير

### تنظيف قاعدة البيانات
```bash
# إعادة تهيئة المناطق
python init_regions_unified.py
```

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
cp database.db backup/database_$(date +%Y%m%d).db
cp stores.db backup/stores_$(date +%Y%m%d).db
```

### السجلات
- سجلات التطبيق: `logs/app.log`
- السجلات الأمنية: `logs/security.log`

## المساهمة

نرحب بالمساهمات! يرجى:
1. إنشاء فرع جديد للميزة
2. إجراء التغييرات
3. اختبار التغييرات
4. إرسال طلب دمج

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.
