/**
 * UnifiedMapManager - نظام موحد لإدارة الخرائط
 * يدير خرائط الكمبيوتر والهاتف بدون تضارب
 * Created: 2024 - Unified System
 */
class UnifiedMapManager {
    static instance = null;
    
    static getInstance() {
        if (!this.instance) {
            this.instance = new UnifiedMapManager();
        }
        return this.instance;
    }
    
    constructor() {
        if (UnifiedMapManager.instance) {
            return UnifiedMapManager.instance;
        }
        
        // خرائط منفصلة لكل واجهة
        this.maps = new Map();
        this.markers = new Map();
        this.selectedLocations = new Map();
        this.isInitialized = false;
        
        // إعدادات افتراضية
        this.defaultCenter = [32.8872, 13.1913]; // طرابلس، ليبيا
        this.defaultZoom = 13;
        
        UnifiedMapManager.instance = this;
    }
    
    /**
     * تهيئة النظام
     */
    async init() {
        if (this.isInitialized) {
            console.log('🗺️ Map Manager already initialized');
            return;
        }
        
        console.log('🗺️ Initializing Unified Map Manager...');
        
        // انتظار تحميل Leaflet
        await this.waitForLeaflet();
        
        // تهيئة الخرائط بناءً على نوع الجهاز
        const deviceDetector = window.unifiedDeviceDetector;
        if (deviceDetector) {
            if (deviceDetector.isMobile) {
                await this.initMobileMap();
            } else {
                await this.initDesktopMap();
                // تهيئة خريطة الهاتف للاختبار
                await this.initDesktopMobileMap();
            }
        }
        
        this.isInitialized = true;
        console.log('✅ Unified Map Manager initialized successfully');
    }
    
    /**
     * انتظار تحميل مكتبة Leaflet
     */
    waitForLeaflet() {
        return new Promise((resolve, reject) => {
            if (typeof L !== 'undefined') {
                resolve(L);
                return;
            }
            
            let attempts = 0;
            const maxAttempts = 20; // 10 ثواني
            
            const checkLeaflet = setInterval(() => {
                attempts++;
                
                if (typeof L !== 'undefined') {
                    clearInterval(checkLeaflet);
                    console.log('✅ Leaflet library loaded');
                    resolve(L);
                } else if (attempts >= maxAttempts) {
                    clearInterval(checkLeaflet);
                    console.error('❌ Leaflet library failed to load');
                    reject(new Error('Leaflet library not available'));
                }
            }, 500);
        });
    }
    
    /**
     * تهيئة خريطة الكمبيوتر
     */
    async initDesktopMap() {
        const mapElement = document.getElementById('map');
        if (!mapElement) {
            console.log('Desktop map element not found');
            return null;
        }
        
        try {
            console.log('🖥️ Initializing desktop map...');
            
            const map = L.map('map', {
                center: this.defaultCenter,
                zoom: this.defaultZoom,
                zoomControl: true,
                dragging: true,
                touchZoom: true,
                scrollWheelZoom: true,
                doubleClickZoom: true
            });
            
            // إضافة طبقات الخريطة
            const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; OpenStreetMap contributors'
            }).addTo(map);
            
            const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles &copy; Esri'
            });
            
            // إضافة تحكم الطبقات
            const baseMaps = {
                "خريطة عادية": osmLayer,
                "قمر صناعي": satelliteLayer
            };
            
            L.control.layers(baseMaps).addTo(map);
            
            // إضافة مستمع النقر
            map.on('click', (e) => {
                this.setSelectedLocation('desktop', e.latlng);
            });
            
            // حفظ الخريطة
            this.maps.set('desktop', map);
            this.markers.set('desktop', []);
            
            console.log('✅ Desktop map initialized successfully');
            return map;
            
        } catch (error) {
            console.error('❌ Failed to initialize desktop map:', error);
            return null;
        }
    }
    
    /**
     * تهيئة خريطة الهاتف المحمول
     */
    async initMobileMap() {
        const mapElement = document.getElementById('mobile-map');
        if (!mapElement) {
            console.log('Mobile map element not found');
            return null;
        }
        
        try {
            console.log('📱 Initializing mobile map...');
            
            // تنظيف العنصر إذا كان يحتوي على خريطة سابقة
            this.cleanMapElement('mobile-map');
            
            const map = L.map('mobile-map', {
                center: this.defaultCenter,
                zoom: this.defaultZoom,
                zoomControl: true,
                dragging: true,
                touchZoom: true,
                scrollWheelZoom: true,
                doubleClickZoom: true,
                tap: true,
                tapTolerance: 15
            });
            
            // إضافة طبقات الخريطة
            const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; OpenStreetMap contributors'
            }).addTo(map);
            
            const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles &copy; Esri'
            });
            
            // إضافة تحكم الطبقات
            const baseMaps = {
                "الخريطة": osmLayer,
                "القمر الصناعي": satelliteLayer
            };
            
            L.control.layers(baseMaps, null, {
                position: 'topright',
                collapsed: true
            }).addTo(map);
            
            // إضافة مستمع النقر
            map.on('click', (e) => {
                this.setSelectedLocation('mobile', e.latlng);
            });
            
            // تحديث حجم الخريطة بعد التهيئة
            setTimeout(() => {
                map.invalidateSize();
            }, 100);
            
            // حفظ الخريطة
            this.maps.set('mobile', map);
            this.markers.set('mobile', []);
            
            console.log('✅ Mobile map initialized successfully');
            return map;
            
        } catch (error) {
            console.error('❌ Failed to initialize mobile map:', error);
            return null;
        }
    }
    
    /**
     * تهيئة خريطة الهاتف في واجهة الكمبيوتر (للاختبار)
     */
    async initDesktopMobileMap() {
        const mapElement = document.getElementById('desktop-mobile-map');
        if (!mapElement) {
            console.log('Desktop mobile map element not found');
            return null;
        }
        
        try {
            console.log('🖥️📱 Initializing desktop mobile map...');
            
            const map = L.map('desktop-mobile-map', {
                center: this.defaultCenter,
                zoom: this.defaultZoom,
                zoomControl: true,
                dragging: true,
                touchZoom: true,
                scrollWheelZoom: true,
                doubleClickZoom: true
            });
            
            // إضافة طبقة الخريطة
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; OpenStreetMap contributors'
            }).addTo(map);
            
            // إضافة مستمع النقر
            map.on('click', (e) => {
                this.setSelectedLocation('desktop-mobile', e.latlng);
            });
            
            // حفظ الخريطة
            this.maps.set('desktop-mobile', map);
            this.markers.set('desktop-mobile', []);
            
            console.log('✅ Desktop mobile map initialized successfully');
            return map;
            
        } catch (error) {
            console.error('❌ Failed to initialize desktop mobile map:', error);
            return null;
        }
    }
    
    /**
     * تنظيف عنصر الخريطة
     */
    cleanMapElement(elementId) {
        const element = document.getElementById(elementId);
        if (element && element._leaflet_id) {
            console.log(`🧹 Cleaning map element: ${elementId}`);
            
            // إنشاء عنصر جديد
            const newElement = document.createElement('div');
            newElement.id = elementId;
            newElement.className = element.className;
            newElement.style.cssText = element.style.cssText;
            
            // استبدال العنصر
            if (element.parentNode) {
                element.parentNode.replaceChild(newElement, element);
            }
        }
    }
    
    /**
     * تعيين الموقع المحدد
     */
    setSelectedLocation(mapType, latlng) {
        const map = this.maps.get(mapType);
        if (!map) return;
        
        console.log(`📍 Setting selected location for ${mapType}:`, latlng);
        
        // حفظ الموقع
        this.selectedLocations.set(mapType, latlng);
        
        // إزالة العلامة السابقة
        this.clearSelectedMarker(mapType);
        
        // إضافة علامة جديدة
        const marker = L.marker(latlng, {
            icon: L.divIcon({
                html: '<i class="fas fa-map-marker-alt" style="color: #d50000; font-size: 24px;"></i>',
                iconSize: [24, 24],
                iconAnchor: [12, 24],
                className: 'selected-location-marker'
            }),
            isSelected: true
        }).addTo(map);
        
        // حفظ العلامة
        const markers = this.markers.get(mapType) || [];
        markers.push(marker);
        this.markers.set(mapType, markers);
        
        // تحديث النص في الواجهة
        this.updateLocationText(mapType, latlng);
        
        // إشعار النظام بالتغيير
        this.notifyLocationChange(mapType, latlng);
    }
    
    /**
     * مسح العلامة المحددة
     */
    clearSelectedMarker(mapType) {
        const map = this.maps.get(mapType);
        const markers = this.markers.get(mapType) || [];
        
        if (map) {
            markers.forEach((marker, index) => {
                if (marker.options && marker.options.isSelected) {
                    map.removeLayer(marker);
                    markers.splice(index, 1);
                }
            });
            
            this.markers.set(mapType, markers);
        }
    }
    
    /**
     * تحديث نص الموقع في الواجهة
     */
    updateLocationText(mapType, latlng) {
        let elementId;
        
        if (mapType === 'mobile') {
            elementId = 'mobile-selected-location';
        } else if (mapType === 'desktop') {
            elementId = 'selectedLocation';
        }
        
        if (elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = `${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`;
            }
        }
    }
    
    /**
     * إشعار النظام بتغيير الموقع
     */
    notifyLocationChange(mapType, latlng) {
        // إرسال حدث مخصص
        const event = new CustomEvent('locationChanged', {
            detail: { mapType, latlng }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * الحصول على خريطة معينة
     */
    getMap(mapType) {
        return this.maps.get(mapType);
    }
    
    /**
     * الحصول على الموقع المحدد
     */
    getSelectedLocation(mapType) {
        return this.selectedLocations.get(mapType);
    }
    
    /**
     * تحديث حجم جميع الخرائط
     */
    invalidateAllMaps() {
        this.maps.forEach((map, mapType) => {
            try {
                map.invalidateSize();
                console.log(`🔄 Map size updated: ${mapType}`);
            } catch (error) {
                console.warn(`Failed to update map size for ${mapType}:`, error);
            }
        });
    }
    
    /**
     * تحديث حجم خريطة معينة
     */
    invalidateMap(mapType) {
        const map = this.maps.get(mapType);
        if (map) {
            try {
                map.invalidateSize();
                console.log(`🔄 Map size updated: ${mapType}`);
            } catch (error) {
                console.warn(`Failed to update map size for ${mapType}:`, error);
            }
        }
    }
    
    /**
     * إضافة علامة متجر
     */
    addStoreMarker(mapType, store) {
        const map = this.maps.get(mapType);
        if (!map || !store.latitude || !store.longitude) return null;
        
        const marker = L.marker([store.latitude, store.longitude], {
            icon: L.divIcon({
                html: '<i class="fas fa-store" style="color: #007bff; font-size: 16px;"></i>',
                iconSize: [16, 16],
                iconAnchor: [8, 16],
                className: 'store-marker'
            }),
            storeId: store.id
        }).addTo(map);
        
        // إضافة نافذة منبثقة
        marker.bindPopup(`
            <div class="text-center">
                <strong>${store.name}</strong><br>
                <small>${store.phone || ''}</small>
            </div>
        `);
        
        // حفظ العلامة
        const markers = this.markers.get(mapType) || [];
        markers.push(marker);
        this.markers.set(mapType, markers);
        
        return marker;
    }
    
    /**
     * إزالة جميع علامات المتاجر
     */
    clearStoreMarkers(mapType) {
        const map = this.maps.get(mapType);
        const markers = this.markers.get(mapType) || [];
        
        if (map) {
            markers.forEach((marker, index) => {
                if (marker.options && marker.options.storeId) {
                    map.removeLayer(marker);
                    markers.splice(index, 1);
                }
            });
            
            this.markers.set(mapType, markers);
        }
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async () => {
    // انتظار تهيئة كاشف الأجهزة
    setTimeout(async () => {
        window.unifiedMapManager = UnifiedMapManager.getInstance();
        await window.unifiedMapManager.init();
        console.log('🗺️ Unified Map Manager ready');
    }, 500);
});
