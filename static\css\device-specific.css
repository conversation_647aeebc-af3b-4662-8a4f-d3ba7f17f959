/**
 * أنماط متطورة للأجهزة المختلفة - نسخة محسنة
 * تم التحديث ليدعم أحدث تقنيات الويب ويتوافق مع جميع الأجهزة
 */

:root {
    /* نظام الألوان المعدل */
    --primary-color: #4285f4;
    --secondary-color: #34a853;
    --danger-color: #ea4335;
    --warning-color: #fbbc05;
    --light-color: #f8f9fa;
    --dark-color: #202124;
    
    /* تحسين المتغيرات السابقة */
    --device-width: 100vw;
    --device-height: 100vh;
    --device-pixel-ratio: 1;
    --device-type: "unknown";
    --device-model: "unknown";
    --safe-area-inset-top: env(safe-area-inset-top, 0px);
    --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
    --safe-area-inset-left: env(safe-area-inset-left, 0px);
    --safe-area-inset-right: env(safe-area-inset-right, 0px);
    --font-size-multiplier: 1;
    --device-font-family: "Tajawal", "Cairo", -apple-system, BlinkMacSystemFont, sans-serif;
    
    /* إضافة متغيرات جديدة */
    --card-border-radius: 12px;
    --button-border-radius: 8px;
    --input-border-radius: 8px;
    --element-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
}

/* تحسينات عامة لجميع الأجهزة */
body.mobile-device {
    font-family: var(--device-font-family);
    font-size: calc(14px * var(--font-size-multiplier));
    padding: var(--safe-area-inset-top) var(--safe-area-inset-right) 
             var(--safe-area-inset-bottom) var(--safe-area-inset-left);
    background-color: var(--light-color);
    color: var(--dark-color);
    line-height: 1.6;
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

/* تحسينات الهواتف الصغيرة */
body.mobile-device.small-phone {
    --font-size-multiplier: 0.9;
    --card-border-radius: 10px;
    --button-border-radius: 6px;
    --input-border-radius: 6px;
}

/* تحسينات الهواتف الكبيرة */
body.mobile-device.large-phone {
    --font-size-multiplier: 1.1;
    --card-border-radius: 14px;
    --button-border-radius: 10px;
    --input-border-radius: 10px;
}

/* أنماط خاصة بكل نوع جهاز */
body.mobile-device.iphone-device {
    --primary-color: #007aff;
    --secondary-color: #34c759;
    --danger-color: #ff3b30;
    --button-border-radius: 10px;
    --card-border-radius: 14px;
}

body.mobile-device.xiaomi-device {
    --primary-color: #0e87fa;
    --secondary-color: #ff6700;
    --button-border-radius: 6px;
    --card-border-radius: 10px;
}

body.mobile-device.samsung-device {
    --primary-color: #1428a0;
    --secondary-color: #75b7ff;
}

/* تحسينات الواجهة المتنقلة */
.mobile-view {
    display: none;
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    padding: 0;
    background-color: var(--light-color);
    color: var(--dark-color);
    overflow-x: hidden;
    box-sizing: border-box;
}

/* تحسينات الاتجاهات */
body.portrait-mode .mobile-view {
    height: calc(100vh - var(--safe-area-inset-top) - var(--safe-area-inset-bottom));
}

body.landscape-mode .mobile-view {
    height: calc(100vh - var(--safe-area-inset-top) - var(--safe-area-inset-bottom));
    max-width: 100%;
}

/* تحسينات العناصر التفاعلية */
.mobile-view button,
.mobile-view .btn {
    padding: 0.75rem 1.25rem;
    border-radius: var(--button-border-radius);
    font-weight: 500;
    background-color: var(--primary-color);
    color: white;
    border: none;
    transition: all var(--transition-speed) ease;
    cursor: pointer;
}

.mobile-view button:hover,
.mobile-view .btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

.mobile-view .card {
    border-radius: var(--card-border-radius);
    box-shadow: var(--element-shadow);
    margin-bottom: 1.5rem;
    background-color: white;
    overflow: hidden;
    transition: transform var(--transition-speed) ease;
}

.mobile-view .card:hover {
    transform: translateY(-3px);
}

/* تحسينات خاصة بكل جهاز */
.mobile-view.iphone-view {
    font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", sans-serif;
}

.mobile-view.xiaomi-view {
    font-family: "Mi Sans", "Roboto", sans-serif;
}

.mobile-view.samsung-view {
    font-family: "Samsung Sans", "Roboto", sans-serif;
}

/* تحسينات النصوص */
.mobile-view h1 {
    font-size: calc(1.75rem * var(--font-size-multiplier));
    margin-bottom: 1.5rem;
}

.mobile-view h2 {
    font-size: calc(1.5rem * var(--font-size-multiplier));
    margin-bottom: 1.25rem;
}

.mobile-view h3 {
    font-size: calc(1.25rem * var(--font-size-multiplier));
    margin-bottom: 1rem;
}

.mobile-view p {
    font-size: calc(1rem * var(--font-size-multiplier));
    margin-bottom: 1rem;
    line-height: 1.6;
}

/* تحسينات المدخلات */
.mobile-view input,
.mobile-view select,
.mobile-view textarea {
    padding: 0.75rem;
    border-radius: var(--input-border-radius);
    border: 1px solid #ced4da;
    width: 100%;
    box-sizing: border-box;
    font-size: calc(1rem * var(--font-size-multiplier));
    transition: border-color var(--transition-speed) ease;
}

.mobile-view input:focus,
.mobile-view select:focus,
.mobile-view textarea:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* تحسينات الوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #121212;
        --dark-color: #f8f9fa;
        --element-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
    
    .mobile-view .card {
        background-color: #1e1e1e;
        border-color: #333;
    }
    
    .mobile-view input,
    .mobile-view select,
    .mobile-view textarea {
        background-color: #2a2a2a;
        border-color: #444;
        color: var(--dark-color);
    }
}

/* تحسينات للشاشات الكبيرة (تجاوز 600px) */
@media (min-width: 600px) {
    .mobile-view {
        max-width: 600px;
    }
}

/* تحسينات الأداء والرسوم المتحركة */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}