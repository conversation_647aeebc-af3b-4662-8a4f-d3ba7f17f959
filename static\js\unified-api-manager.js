/**
 * UnifiedAPIManager - نظام موحد لإدارة طلبات API
 * يدير جميع طلبات الشبكة مع معالجة محسنة للأخطاء
 * Created: 2024 - Unified System
 */
class UnifiedAPIManager {
    static instance = null;
    
    static getInstance() {
        if (!this.instance) {
            this.instance = new UnifiedAPIManager();
        }
        return this.instance;
    }
    
    constructor() {
        if (UnifiedAPIManager.instance) {
            return UnifiedAPIManager.instance;
        }
        
        // إعدادات API
        this.baseURL = '/api';
        this.timeout = 30000; // 30 ثانية
        this.retryAttempts = 3;
        this.retryDelay = 1000; // 1 ثانية
        
        // ذاكرة التخزين المؤقت
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 دقائق
        
        // طلبات جارية
        this.pendingRequests = new Map();
        
        // إحصائيات
        this.stats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            cachedRequests: 0,
            retryAttempts: 0
        };
        
        UnifiedAPIManager.instance = this;
        console.log('🌐 Unified API Manager initialized');
    }
    
    /**
     * الحصول على CSRF Token
     */
    getCSRFToken() {
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        return metaTag ? metaTag.getAttribute('content') : null;
    }
    
    /**
     * إنشاء مفتاح للذاكرة المؤقتة
     */
    createCacheKey(url, options = {}) {
        const method = options.method || 'GET';
        const body = options.body || '';
        return `${method}:${url}:${body}`;
    }
    
    /**
     * التحقق من الذاكرة المؤقتة
     */
    getFromCache(cacheKey) {
        const cached = this.cache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            this.stats.cachedRequests++;
            console.log(`📦 Cache hit for: ${cacheKey}`);
            return cached.data;
        }
        
        // إزالة البيانات المنتهية الصلاحية
        if (cached) {
            this.cache.delete(cacheKey);
        }
        
        return null;
    }
    
    /**
     * حفظ في الذاكرة المؤقتة
     */
    saveToCache(cacheKey, data) {
        this.cache.set(cacheKey, {
            data: data,
            timestamp: Date.now()
        });
        
        console.log(`💾 Cached response for: ${cacheKey}`);
    }
    
    /**
     * تنفيذ طلب HTTP مع إعادة المحاولة
     */
    async executeRequest(url, options = {}, attempt = 1) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        
        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': this.getCSRFToken(),
                    ...options.headers
                }
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            this.stats.successfulRequests++;
            
            return data;
            
        } catch (error) {
            clearTimeout(timeoutId);
            
            // إعادة المحاولة في حالة فشل الشبكة
            if (attempt < this.retryAttempts && this.shouldRetry(error)) {
                this.stats.retryAttempts++;
                console.warn(`🔄 Retrying request (${attempt}/${this.retryAttempts}): ${url}`);
                
                await this.delay(this.retryDelay * attempt);
                return this.executeRequest(url, options, attempt + 1);
            }
            
            this.stats.failedRequests++;
            throw error;
        }
    }
    
    /**
     * تحديد ما إذا كان يجب إعادة المحاولة
     */
    shouldRetry(error) {
        // إعادة المحاولة في حالة مشاكل الشبكة
        return error.name === 'TypeError' || 
               error.name === 'AbortError' ||
               error.message.includes('Failed to fetch') ||
               error.message.includes('Network request failed');
    }
    
    /**
     * تأخير لإعادة المحاولة
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * طلب GET مع ذاكرة تخزين مؤقت
     */
    async get(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const cacheKey = this.createCacheKey(url, { method: 'GET' });
        
        this.stats.totalRequests++;
        
        // التحقق من الذاكرة المؤقتة
        if (options.useCache !== false) {
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                return cached;
            }
        }
        
        // التحقق من الطلبات الجارية
        if (this.pendingRequests.has(cacheKey)) {
            console.log(`⏳ Waiting for pending request: ${endpoint}`);
            return this.pendingRequests.get(cacheKey);
        }
        
        // تنفيذ الطلب
        const requestPromise = this.executeRequest(url, {
            method: 'GET',
            ...options
        });
        
        this.pendingRequests.set(cacheKey, requestPromise);
        
        try {
            const data = await requestPromise;
            
            // حفظ في الذاكرة المؤقتة
            if (options.useCache !== false) {
                this.saveToCache(cacheKey, data);
            }
            
            return data;
            
        } finally {
            this.pendingRequests.delete(cacheKey);
        }
    }
    
    /**
     * طلب POST
     */
    async post(endpoint, data = {}, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        this.stats.totalRequests++;
        
        return this.executeRequest(url, {
            method: 'POST',
            body: JSON.stringify(data),
            ...options
        });
    }
    
    /**
     * طلب PUT
     */
    async put(endpoint, data = {}, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        this.stats.totalRequests++;
        
        return this.executeRequest(url, {
            method: 'PUT',
            body: JSON.stringify(data),
            ...options
        });
    }
    
    /**
     * طلب DELETE
     */
    async delete(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        this.stats.totalRequests++;
        
        return this.executeRequest(url, {
            method: 'DELETE',
            ...options
        });
    }
    
    /**
     * تحميل المتاجر
     */
    async loadStores(listId = null) {
        try {
            const endpoint = listId ? `/stores?list_id=${listId}` : '/stores';
            const response = await this.get(endpoint);
            
            if (response.success) {
                console.log(`✅ Loaded ${response.stores.length} stores`);
                return response.stores;
            } else {
                throw new Error(response.message || 'Failed to load stores');
            }
            
        } catch (error) {
            console.error('❌ Error loading stores:', error);
            this.handleError('تحميل المتاجر', error);
            throw error;
        }
    }
    
    /**
     * تحميل القوائم المخصصة
     */
    async loadCustomLists() {
        try {
            const response = await this.get('/custom-lists');
            
            if (response.success) {
                console.log(`✅ Loaded ${response.lists.length} custom lists`);
                return response.lists;
            } else {
                throw new Error(response.message || 'Failed to load custom lists');
            }
            
        } catch (error) {
            console.error('❌ Error loading custom lists:', error);
            this.handleError('تحميل القوائم', error);
            throw error;
        }
    }
    
    /**
     * حفظ متجر جديد أو تحديث موجود
     */
    async saveStore(storeData, storeId = null) {
        try {
            let response;
            
            if (storeId) {
                // تحديث متجر موجود
                response = await this.put(`/stores/${storeId}`, storeData);
                console.log(`✅ Store updated: ${storeId}`);
            } else {
                // إضافة متجر جديد
                response = await this.post('/stores', storeData);
                console.log(`✅ New store created`);
            }
            
            if (response.success) {
                // مسح الذاكرة المؤقتة للمتاجر
                this.clearStoresCache();
                return response;
            } else {
                throw new Error(response.message || 'Failed to save store');
            }
            
        } catch (error) {
            console.error('❌ Error saving store:', error);
            this.handleError('حفظ المتجر', error);
            throw error;
        }
    }
    
    /**
     * حذف متجر
     */
    async deleteStore(storeId) {
        try {
            const response = await this.delete(`/stores?id=${storeId}`);
            
            if (response.success) {
                console.log(`✅ Store deleted: ${storeId}`);
                // مسح الذاكرة المؤقتة للمتاجر
                this.clearStoresCache();
                return response;
            } else {
                throw new Error(response.message || 'Failed to delete store');
            }
            
        } catch (error) {
            console.error('❌ Error deleting store:', error);
            this.handleError('حذف المتجر', error);
            throw error;
        }
    }
    
    /**
     * رفع صورة
     */
    async uploadImage(file, storeId = null) {
        try {
            const formData = new FormData();
            formData.append('image', file);
            if (storeId) {
                formData.append('store_id', storeId);
            }
            
            const response = await fetch('/api/upload-image', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-Token': this.getCSRFToken()
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                console.log(`✅ Image uploaded: ${data.image_path}`);
                return data;
            } else {
                throw new Error(data.message || 'Failed to upload image');
            }
            
        } catch (error) {
            console.error('❌ Error uploading image:', error);
            this.handleError('رفع الصورة', error);
            throw error;
        }
    }
    
    /**
     * مسح ذاكرة التخزين المؤقت للمتاجر
     */
    clearStoresCache() {
        const keysToDelete = [];
        for (const key of this.cache.keys()) {
            if (key.includes('/stores')) {
                keysToDelete.push(key);
            }
        }
        
        keysToDelete.forEach(key => this.cache.delete(key));
        console.log(`🗑️ Cleared ${keysToDelete.length} store cache entries`);
    }
    
    /**
     * مسح جميع ذاكرة التخزين المؤقت
     */
    clearAllCache() {
        this.cache.clear();
        console.log('🗑️ All cache cleared');
    }
    
    /**
     * معالجة الأخطاء
     */
    handleError(operation, error) {
        let message = `حدث خطأ أثناء ${operation}`;
        
        if (error.name === 'AbortError') {
            message += ': انتهت مهلة الطلب';
        } else if (error.message.includes('Failed to fetch')) {
            message += ': تحقق من اتصال الإنترنت';
        } else if (error.message.includes('HTTP 404')) {
            message += ': الصفحة غير موجودة';
        } else if (error.message.includes('HTTP 500')) {
            message += ': خطأ في الخادم';
        } else {
            message += `: ${error.message}`;
        }
        
        // إظهار رسالة للمستخدم
        this.showUserMessage(message, 'error');
    }
    
    /**
     * إظهار رسالة للمستخدم
     */
    showUserMessage(message, type = 'info') {
        // محاولة استخدام نظام الإشعارات إذا كان متوفراً
        if (window.showAlert) {
            window.showAlert(message, type);
        } else {
            // استخدام alert كبديل
            alert(message);
        }
        
        console.log(`📢 User message (${type}): ${message}`);
    }
    
    /**
     * الحصول على إحصائيات API
     */
    getStats() {
        return {
            ...this.stats,
            cacheSize: this.cache.size,
            pendingRequests: this.pendingRequests.size,
            successRate: this.stats.totalRequests > 0 ? 
                (this.stats.successfulRequests / this.stats.totalRequests * 100).toFixed(2) + '%' : '0%'
        };
    }
    
    /**
     * إعادة تعيين الإحصائيات
     */
    resetStats() {
        this.stats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            cachedRequests: 0,
            retryAttempts: 0
        };
        console.log('📊 API stats reset');
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.unifiedAPIManager = UnifiedAPIManager.getInstance();
    console.log('🌐 Unified API Manager ready');
});
