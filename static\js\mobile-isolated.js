/**
 * نظام منفصل تماماً لواجهة الهاتف المحمول
 * لا يؤثر على واجهة الكمبيوتر نهائياً
 */

class MobileAppManager {
    constructor() {
        // متغيرات منفصلة تماماً عن الكمبيوتر
        this.mobileMap = null;
        this.mobileStores = [];
        this.mobileSelectedLocation = null;
        this.mobileMarkers = [];
        this.mobileCurrentListId = null;
        
        // التحقق من أن هذا جهاز محمول
        if (!this.isMobileDevice()) {
            console.log('Not a mobile device, skipping mobile initialization');
            return;
        }
        
        this.init();
    }
    
    isMobileDevice() {
        // كشف دقيق للجهاز المحمول
        const userAgent = navigator.userAgent;
        const screenWidth = window.innerWidth;
        const mobileKeywords = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
        
        return screenWidth < 768 || mobileKeywords.test(userAgent);
    }
    
    init() {
        console.log('🚀 Initializing Mobile App Manager...');
        
        // التحقق من وجود واجهة الهاتف
        const mobileView = document.querySelector('.mobile-view');
        if (!mobileView) {
            console.log('Mobile view not found');
            return;
        }
        
        // إظهار واجهة الهاتف وإخفاء واجهة الكمبيوتر
        this.showMobileInterface();
        
        // تهيئة الخريطة
        this.initMobileMap();
        
        // تهيئة الأحداث
        this.setupMobileEvents();
        
        // تحميل البيانات
        this.loadMobileData();
        
        console.log('✅ Mobile App Manager initialized successfully');
    }
    
    showMobileInterface() {
        // إخفاء واجهة الكمبيوتر
        const desktopView = document.querySelector('.container-fluid');
        if (desktopView) {
            desktopView.style.display = 'none';
        }
        
        // إظهار واجهة الهاتف
        const mobileView = document.querySelector('.mobile-view');
        if (mobileView) {
            mobileView.style.display = 'block';
        }
        
        // إضافة فئة للجسم
        document.body.classList.add('mobile-active');
        document.body.classList.remove('desktop-active');
    }
    
    initMobileMap() {
        console.log('🗺️ Initializing mobile map...');
        
        // التحقق من وجود عنصر الخريطة
        const mapElement = document.getElementById('mobile-map');
        if (!mapElement) {
            console.error('Mobile map element not found');
            return;
        }
        
        // التحقق من تحميل Leaflet
        if (typeof L === 'undefined') {
            console.error('Leaflet library not loaded');
            return;
        }
        
        try {
            // إنشاء خريطة جديدة
            this.mobileMap = L.map('mobile-map', {
                center: [32.8872, 13.1913], // طرابلس، ليبيا
                zoom: 13,
                zoomControl: true,
                dragging: true,
                touchZoom: true,
                scrollWheelZoom: true,
                doubleClickZoom: true
            });
            
            // إضافة طبقة الخريطة
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; OpenStreetMap contributors'
            }).addTo(this.mobileMap);
            
            // إضافة مستمع النقر
            this.mobileMap.on('click', (e) => {
                this.setSelectedLocation(e.latlng);
            });
            
            console.log('✅ Mobile map initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize mobile map:', error);
        }
    }
    
    setSelectedLocation(latlng) {
        this.mobileSelectedLocation = latlng;
        
        // إزالة العلامة السابقة
        this.mobileMarkers.forEach(marker => {
            if (marker.options.isSelected) {
                this.mobileMap.removeLayer(marker);
            }
        });
        
        // إضافة علامة جديدة
        const marker = L.marker(latlng, {
            icon: L.divIcon({
                html: '<i class="fas fa-map-marker-alt" style="color: #d50000; font-size: 24px;"></i>',
                iconSize: [24, 24],
                iconAnchor: [12, 24],
                className: 'mobile-selected-marker'
            }),
            isSelected: true
        }).addTo(this.mobileMap);
        
        this.mobileMarkers.push(marker);
        
        // تحديث النص
        const locationText = document.getElementById('mobile-selected-location');
        if (locationText) {
            locationText.textContent = `${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`;
        }
        
        console.log('📍 Mobile location selected:', latlng);
    }
    
    setupMobileEvents() {
        console.log('🔧 Setting up mobile events...');
        
        // أحداث التبويبات
        this.setupTabEvents();
        
        // أحداث النماذج
        this.setupFormEvents();
        
        // أحداث الأزرار
        this.setupButtonEvents();
    }
    
    setupTabEvents() {
        const tabButtons = document.querySelectorAll('.mobile-tab-btn');
        tabButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabId = e.target.dataset.tab;
                this.switchTab(tabId);
            });
        });
    }
    
    switchTab(tabId) {
        // إخفاء جميع التبويبات
        document.querySelectorAll('.mobile-tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        
        // إزالة الفئة النشطة من جميع الأزرار
        document.querySelectorAll('.mobile-tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // إظهار التبويب المحدد
        const targetTab = document.getElementById(tabId);
        if (targetTab) {
            targetTab.classList.add('active');
        }
        
        // تفعيل الزر المحدد
        const targetBtn = document.querySelector(`[data-tab="${tabId}"]`);
        if (targetBtn) {
            targetBtn.classList.add('active');
        }
        
        // تحديث حجم الخريطة إذا كان التبويب هو الخريطة
        if (tabId === 'mobile-map-tab' && this.mobileMap) {
            setTimeout(() => {
                this.mobileMap.invalidateSize();
            }, 100);
        }
    }
    
    setupFormEvents() {
        const form = document.getElementById('mobile-store-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveMobileStore();
            });
        }
    }
    
    setupButtonEvents() {
        // زر الموقع الحالي
        const getCurrentLocationBtn = document.getElementById('mobile-get-location');
        if (getCurrentLocationBtn) {
            getCurrentLocationBtn.addEventListener('click', () => {
                this.getCurrentLocation();
            });
        }
    }
    
    getCurrentLocation() {
        if (!navigator.geolocation) {
            alert('الموقع الجغرافي غير مدعوم في هذا المتصفح');
            return;
        }
        
        navigator.geolocation.getCurrentPosition(
            (position) => {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                
                const latlng = L.latLng(lat, lng);
                this.setSelectedLocation(latlng);
                
                // الانتقال إلى الموقع
                this.mobileMap.setView(latlng, 16, {
                    animate: true,
                    duration: 1
                });
                
                console.log('📍 Current location found:', latlng);
            },
            (error) => {
                console.error('Error getting location:', error);
                alert('حدث خطأ في تحديد الموقع');
            }
        );
    }
    
    async loadMobileData() {
        console.log('📊 Loading mobile data...');

        try {
            // استخدام API Manager الموحد
            const apiManager = window.unifiedAPIManager;
            if (apiManager) {
                // تحميل المتاجر
                await this.loadMobileStores();

                // تحميل القوائم
                await this.loadMobileLists();
            } else {
                console.warn('Unified API Manager not available, using fallback');
                await this.loadMobileDataFallback();
            }

        } catch (error) {
            console.error('Error loading mobile data:', error);
            window.unifiedAPIManager?.handleError('تحميل البيانات', error);
        }
    }

    async loadMobileStores() {
        try {
            const apiManager = window.unifiedAPIManager;
            const stores = await apiManager.loadStores();

            this.mobileStores = stores;
            this.renderMobileStores();
            this.renderMobileStoreMarkers();

            // تحديث الحالة
            if (window.unifiedStateManager) {
                window.unifiedStateManager.updateStores(stores);
            }

            console.log('✅ Mobile stores loaded:', this.mobileStores.length);

        } catch (error) {
            console.error('Error loading mobile stores:', error);
            throw error;
        }
    }

    async loadMobileLists() {
        try {
            const apiManager = window.unifiedAPIManager;
            const lists = await apiManager.loadCustomLists();

            this.mobileLists = lists;

            // تحديث الحالة
            if (window.unifiedStateManager) {
                window.unifiedStateManager.setState('lists.available', lists);
            }

            console.log('✅ Mobile lists loaded:', this.mobileLists.length);

        } catch (error) {
            console.error('Error loading mobile lists:', error);
            throw error;
        }
    }

    async loadMobileDataFallback() {
        // نسخة احتياطية للتحميل المباشر
        try {
            const [storesResponse, listsResponse] = await Promise.all([
                fetch('/api/stores'),
                fetch('/api/custom-lists')
            ]);

            const storesData = await storesResponse.json();
            const listsData = await listsResponse.json();

            if (storesData.success) {
                this.mobileStores = storesData.stores;
                this.renderMobileStores();
                this.renderMobileStoreMarkers();
            }

            if (listsData.success) {
                this.mobileLists = listsData.lists;
            }

        } catch (error) {
            console.error('Fallback data loading failed:', error);
        }
    }
    
    renderMobileStores() {
        const storeList = document.getElementById('mobile-store-list');
        if (!storeList) return;
        
        storeList.innerHTML = '';
        
        this.mobileStores.forEach(store => {
            const storeElement = this.createMobileStoreElement(store);
            storeList.appendChild(storeElement);
        });
    }
    
    createMobileStoreElement(store) {
        const div = document.createElement('div');
        div.className = 'mobile-store-card card mb-2';
        div.innerHTML = `
            <div class="card-body">
                <h6 class="card-title">${store.name}</h6>
                <p class="card-text small text-muted">${store.phone || ''}</p>
                <div class="btn-group w-100">
                    <button class="btn btn-sm btn-outline-primary" onclick="mobileApp.locateStore(${store.id})">
                        <i class="fas fa-map-marker-alt"></i> الموقع
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="mobileApp.editStore(${store.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                </div>
            </div>
        `;
        return div;
    }
    
    renderMobileStoreMarkers() {
        if (!this.mobileMap) return;
        
        // إزالة العلامات السابقة (باستثناء المحددة)
        this.mobileMarkers = this.mobileMarkers.filter(marker => {
            if (!marker.options.isSelected) {
                this.mobileMap.removeLayer(marker);
                return false;
            }
            return true;
        });
        
        // إضافة علامات المتاجر
        this.mobileStores.forEach(store => {
            if (store.latitude && store.longitude) {
                const marker = L.marker([store.latitude, store.longitude], {
                    icon: L.divIcon({
                        html: '<i class="fas fa-store" style="color: #007bff; font-size: 16px;"></i>',
                        iconSize: [16, 16],
                        iconAnchor: [8, 16],
                        className: 'mobile-store-marker'
                    }),
                    storeId: store.id
                }).addTo(this.mobileMap);
                
                // إضافة نافذة منبثقة
                marker.bindPopup(`
                    <div class="text-center">
                        <strong>${store.name}</strong><br>
                        <small>${store.phone || ''}</small>
                    </div>
                `);
                
                this.mobileMarkers.push(marker);
            }
        });
    }
    
    locateStore(storeId) {
        const store = this.mobileStores.find(s => s.id === storeId);
        if (store && store.latitude && store.longitude) {
            this.mobileMap.setView([store.latitude, store.longitude], 16, {
                animate: true,
                duration: 1
            });
            
            // التبديل إلى تبويب الخريطة
            this.switchTab('mobile-map-tab');
        }
    }
    
    editStore(storeId) {
        // التبديل إلى تبويب النموذج
        this.switchTab('mobile-form-tab');
        
        // ملء النموذج ببيانات المتجر
        const store = this.mobileStores.find(s => s.id === storeId);
        if (store) {
            document.getElementById('mobile-store-name').value = store.name || '';
            document.getElementById('mobile-store-phone').value = store.phone || '';
            document.getElementById('mobile-store-id').value = store.id;
            
            // تحديد الموقع على الخريطة
            if (store.latitude && store.longitude) {
                this.setSelectedLocation(L.latLng(store.latitude, store.longitude));
            }
        }
    }
    
    async saveMobileStore() {
        const storeId = document.getElementById('mobile-store-id').value;
        const name = document.getElementById('mobile-store-name').value;
        const phone = document.getElementById('mobile-store-phone').value;

        if (!name || !phone) {
            this.showMessage('يرجى ملء جميع الحقول المطلوبة', 'warning');
            return;
        }

        if (!this.mobileSelectedLocation) {
            this.showMessage('يرجى تحديد موقع المتجر على الخريطة', 'warning');
            return;
        }

        const storeData = {
            name: name,
            phone: phone,
            latitude: this.mobileSelectedLocation.lat,
            longitude: this.mobileSelectedLocation.lng
        };

        try {
            // إظهار مؤشر التحميل
            this.setFormLoading(true);

            // استخدام API Manager الموحد
            const apiManager = window.unifiedAPIManager;
            let result;

            if (apiManager) {
                result = await apiManager.saveStore(storeData, storeId || null);
            } else {
                // نسخة احتياطية
                result = await this.saveMobileStoreFallback(storeData, storeId);
            }

            if (result.success) {
                this.showMessage(
                    storeId ? 'تم تحديث المتجر بنجاح' : 'تم إضافة المتجر بنجاح',
                    'success'
                );

                // إعادة تحميل البيانات
                await this.loadMobileStores();

                // مسح النموذج
                this.clearMobileForm();

                // التبديل إلى تبويب الخريطة
                this.switchTab('mobile-map-tab');

                // تحديث الحالة
                if (window.unifiedStateManager) {
                    window.unifiedStateManager.stopEditingStore();
                }
            } else {
                this.showMessage('حدث خطأ: ' + result.message, 'error');
            }

        } catch (error) {
            console.error('Error saving store:', error);
            this.showMessage('حدث خطأ أثناء حفظ المتجر', 'error');
        } finally {
            this.setFormLoading(false);
        }
    }

    async saveMobileStoreFallback(storeData, storeId) {
        const url = storeId ? `/api/stores/${storeId}` : '/api/stores';
        const method = storeId ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.content
            },
            body: JSON.stringify(storeData)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }

    setFormLoading(isLoading) {
        const form = document.getElementById('mobile-store-form');
        const submitBtn = form?.querySelector('button[type="submit"]');

        if (submitBtn) {
            submitBtn.disabled = isLoading;
            submitBtn.innerHTML = isLoading ?
                '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...' :
                '<i class="fas fa-save"></i> حفظ';
        }

        // تعطيل جميع حقول النموذج
        const inputs = form?.querySelectorAll('input, select, textarea');
        inputs?.forEach(input => {
            input.disabled = isLoading;
        });
    }

    showMessage(message, type = 'info') {
        // محاولة استخدام نظام الإشعارات الموحد
        if (window.unifiedAPIManager) {
            window.unifiedAPIManager.showUserMessage(message, type);
        } else {
            // استخدام alert كبديل
            alert(message);
        }
    }
    
    clearMobileForm() {
        document.getElementById('mobile-store-id').value = '';
        document.getElementById('mobile-store-name').value = '';
        document.getElementById('mobile-store-phone').value = '';
        
        // إزالة الموقع المحدد
        this.mobileSelectedLocation = null;
        
        // إزالة العلامة المحددة
        this.mobileMarkers = this.mobileMarkers.filter(marker => {
            if (marker.options.isSelected) {
                this.mobileMap.removeLayer(marker);
                return false;
            }
            return true;
        });
        
        // مسح نص الموقع
        const locationText = document.getElementById('mobile-selected-location');
        if (locationText) {
            locationText.textContent = 'لم يتم تحديد موقع';
        }
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // إنشاء مثيل عام للتطبيق
    window.mobileApp = new MobileAppManager();
});
