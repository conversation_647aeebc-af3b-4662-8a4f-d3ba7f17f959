/**
 * UnifiedAppInitializer - نظام تهيئة موحد للتطبيق
 * يضمن تحميل جميع الأنظمة بالترتيب الصحيح
 * Created: 2024 - Unified System
 */
class UnifiedAppInitializer {
    static instance = null;
    
    static getInstance() {
        if (!this.instance) {
            this.instance = new UnifiedAppInitializer();
        }
        return this.instance;
    }
    
    constructor() {
        if (UnifiedAppInitializer.instance) {
            return UnifiedAppInitializer.instance;
        }
        
        // حالة التهيئة
        this.initializationState = {
            deviceDetector: false,
            stateManager: false,
            apiManager: false,
            mapManager: false,
            performanceOptimizer: false,
            mobileApp: false,
            legacySystems: false,
            isComplete: false
        };
        
        // قائمة انتظار للمهام
        this.initializationQueue = [];
        this.isInitializing = false;
        
        // مستمعي الأحداث
        this.eventListeners = new Map();
        
        UnifiedAppInitializer.instance = this;
        
        console.log('🚀 Unified App Initializer created');
    }
    
    /**
     * بدء عملية التهيئة
     */
    async initialize() {
        if (this.isInitializing) {
            console.log('⏳ Initialization already in progress');
            return;
        }
        
        this.isInitializing = true;
        console.log('🚀 Starting unified app initialization...');
        
        try {
            // المرحلة 1: تهيئة الأنظمة الأساسية
            await this.initializeCore();
            
            // المرحلة 2: تهيئة الأنظمة المتقدمة
            await this.initializeAdvanced();
            
            // المرحلة 3: تهيئة واجهة المستخدم
            await this.initializeUI();
            
            // المرحلة 4: تهيئة الأنظمة القديمة (للتوافق)
            await this.initializeLegacy();
            
            // المرحلة 5: الانتهاء
            await this.finalize();
            
            console.log('✅ Unified app initialization completed successfully');
            
        } catch (error) {
            console.error('❌ App initialization failed:', error);
            this.handleInitializationError(error);
        } finally {
            this.isInitializing = false;
        }
    }
    
    /**
     * تهيئة الأنظمة الأساسية
     */
    async initializeCore() {
        console.log('🔧 Initializing core systems...');
        
        // 1. كاشف الأجهزة
        await this.initializeDeviceDetector();
        
        // 2. مدير الحالة
        await this.initializeStateManager();
        
        // 3. مدير API
        await this.initializeAPIManager();
        
        console.log('✅ Core systems initialized');
    }
    
    /**
     * تهيئة الأنظمة المتقدمة
     */
    async initializeAdvanced() {
        console.log('🔧 Initializing advanced systems...');
        
        // 1. مدير الخرائط
        await this.initializeMapManager();
        
        // 2. محسن الأداء
        await this.initializePerformanceOptimizer();
        
        console.log('✅ Advanced systems initialized');
    }
    
    /**
     * تهيئة واجهة المستخدم
     */
    async initializeUI() {
        console.log('🔧 Initializing user interface...');
        
        // تحديد نوع الواجهة بناءً على الجهاز
        const deviceDetector = window.unifiedDeviceDetector;
        if (deviceDetector && deviceDetector.isMobile) {
            await this.initializeMobileApp();
        }
        
        console.log('✅ User interface initialized');
    }
    
    /**
     * تهيئة الأنظمة القديمة
     */
    async initializeLegacy() {
        console.log('🔧 Initializing legacy systems...');
        
        // تهيئة الأنظمة القديمة للتوافق
        this.initializationState.legacySystems = true;
        
        console.log('✅ Legacy systems initialized');
    }
    
    /**
     * الانتهاء من التهيئة
     */
    async finalize() {
        console.log('🔧 Finalizing initialization...');
        
        // تحديث حالة التهيئة
        this.initializationState.isComplete = true;
        
        // إرسال حدث الانتهاء
        this.emitEvent('initialized', this.initializationState);
        
        // تنفيذ المهام المؤجلة
        await this.executeQueuedTasks();
        
        console.log('✅ Initialization finalized');
    }
    
    /**
     * تهيئة كاشف الأجهزة
     */
    async initializeDeviceDetector() {
        return new Promise((resolve) => {
            if (window.unifiedDeviceDetector) {
                this.initializationState.deviceDetector = true;
                console.log('✅ Device detector ready');
                resolve();
            } else {
                // انتظار تحميل كاشف الأجهزة
                const checkInterval = setInterval(() => {
                    if (window.unifiedDeviceDetector) {
                        clearInterval(checkInterval);
                        this.initializationState.deviceDetector = true;
                        console.log('✅ Device detector ready');
                        resolve();
                    }
                }, 100);
                
                // مهلة زمنية
                setTimeout(() => {
                    clearInterval(checkInterval);
                    console.warn('⚠️ Device detector timeout');
                    resolve();
                }, 5000);
            }
        });
    }
    
    /**
     * تهيئة مدير الحالة
     */
    async initializeStateManager() {
        return new Promise((resolve) => {
            if (window.unifiedStateManager) {
                this.initializationState.stateManager = true;
                console.log('✅ State manager ready');
                resolve();
            } else {
                const checkInterval = setInterval(() => {
                    if (window.unifiedStateManager) {
                        clearInterval(checkInterval);
                        this.initializationState.stateManager = true;
                        console.log('✅ State manager ready');
                        resolve();
                    }
                }, 100);
                
                setTimeout(() => {
                    clearInterval(checkInterval);
                    console.warn('⚠️ State manager timeout');
                    resolve();
                }, 5000);
            }
        });
    }
    
    /**
     * تهيئة مدير API
     */
    async initializeAPIManager() {
        return new Promise((resolve) => {
            if (window.unifiedAPIManager) {
                this.initializationState.apiManager = true;
                console.log('✅ API manager ready');
                resolve();
            } else {
                const checkInterval = setInterval(() => {
                    if (window.unifiedAPIManager) {
                        clearInterval(checkInterval);
                        this.initializationState.apiManager = true;
                        console.log('✅ API manager ready');
                        resolve();
                    }
                }, 100);
                
                setTimeout(() => {
                    clearInterval(checkInterval);
                    console.warn('⚠️ API manager timeout');
                    resolve();
                }, 5000);
            }
        });
    }
    
    /**
     * تهيئة مدير الخرائط
     */
    async initializeMapManager() {
        return new Promise((resolve) => {
            if (window.unifiedMapManager) {
                // تهيئة الخرائط
                window.unifiedMapManager.init().then(() => {
                    this.initializationState.mapManager = true;
                    console.log('✅ Map manager ready');
                    resolve();
                }).catch((error) => {
                    console.warn('⚠️ Map manager initialization failed:', error);
                    resolve();
                });
            } else {
                const checkInterval = setInterval(() => {
                    if (window.unifiedMapManager) {
                        clearInterval(checkInterval);
                        window.unifiedMapManager.init().then(() => {
                            this.initializationState.mapManager = true;
                            console.log('✅ Map manager ready');
                            resolve();
                        }).catch((error) => {
                            console.warn('⚠️ Map manager initialization failed:', error);
                            resolve();
                        });
                    }
                }, 100);
                
                setTimeout(() => {
                    clearInterval(checkInterval);
                    console.warn('⚠️ Map manager timeout');
                    resolve();
                }, 10000);
            }
        });
    }
    
    /**
     * تهيئة محسن الأداء
     */
    async initializePerformanceOptimizer() {
        return new Promise((resolve) => {
            if (window.performanceOptimizer) {
                this.initializationState.performanceOptimizer = true;
                console.log('✅ Performance optimizer ready');
                resolve();
            } else {
                const checkInterval = setInterval(() => {
                    if (window.performanceOptimizer) {
                        clearInterval(checkInterval);
                        this.initializationState.performanceOptimizer = true;
                        console.log('✅ Performance optimizer ready');
                        resolve();
                    }
                }, 100);
                
                setTimeout(() => {
                    clearInterval(checkInterval);
                    console.warn('⚠️ Performance optimizer timeout');
                    resolve();
                }, 5000);
            }
        });
    }
    
    /**
     * تهيئة تطبيق الهاتف
     */
    async initializeMobileApp() {
        return new Promise((resolve) => {
            if (window.mobileApp) {
                this.initializationState.mobileApp = true;
                console.log('✅ Mobile app ready');
                resolve();
            } else {
                const checkInterval = setInterval(() => {
                    if (window.mobileApp) {
                        clearInterval(checkInterval);
                        this.initializationState.mobileApp = true;
                        console.log('✅ Mobile app ready');
                        resolve();
                    }
                }, 100);
                
                setTimeout(() => {
                    clearInterval(checkInterval);
                    console.warn('⚠️ Mobile app timeout');
                    resolve();
                }, 5000);
            }
        });
    }
    
    /**
     * إضافة مهمة إلى قائمة الانتظار
     */
    queueTask(task, priority = 0) {
        this.initializationQueue.push({ task, priority });
        this.initializationQueue.sort((a, b) => b.priority - a.priority);
    }
    
    /**
     * تنفيذ المهام المؤجلة
     */
    async executeQueuedTasks() {
        console.log(`🔄 Executing ${this.initializationQueue.length} queued tasks...`);
        
        for (const { task } of this.initializationQueue) {
            try {
                await task();
            } catch (error) {
                console.error('Error executing queued task:', error);
            }
        }
        
        this.initializationQueue = [];
        console.log('✅ Queued tasks completed');
    }
    
    /**
     * إضافة مستمع حدث
     */
    addEventListener(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }
    
    /**
     * إرسال حدث
     */
    emitEvent(event, data) {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            listeners.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }
    
    /**
     * معالجة أخطاء التهيئة
     */
    handleInitializationError(error) {
        console.error('🚨 Initialization error:', error);
        
        // محاولة التعافي
        this.attemptRecovery();
    }
    
    /**
     * محاولة التعافي من الأخطاء
     */
    attemptRecovery() {
        console.log('🔄 Attempting recovery...');
        
        // إعادة تعيين حالة التهيئة
        this.isInitializing = false;
        
        // محاولة إعادة التهيئة بعد تأخير
        setTimeout(() => {
            this.initialize();
        }, 2000);
    }
    
    /**
     * الحصول على حالة التهيئة
     */
    getInitializationState() {
        return { ...this.initializationState };
    }
    
    /**
     * التحقق من اكتمال التهيئة
     */
    isInitialized() {
        return this.initializationState.isComplete;
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async () => {
    console.log('📄 DOM loaded, starting app initialization...');
    
    // إنشاء مثيل المهيئ
    window.unifiedAppInitializer = UnifiedAppInitializer.getInstance();
    
    // بدء التهيئة
    await window.unifiedAppInitializer.initialize();
    
    console.log('🎉 App ready!');
});
