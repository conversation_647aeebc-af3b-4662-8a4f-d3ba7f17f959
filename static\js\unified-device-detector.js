/**
 * UnifiedDeviceDetector - نظام موحد ومحسن للكشف عن نوع الجهاز
 * يعمل بشكل موثوق ولا يتداخل مع أنظمة أخرى
 * Created: 2024 - Unified System
 */
class UnifiedDeviceDetector {
    // Singleton pattern لضمان وجود مثيل واحد فقط
    static instance = null;
    
    static getInstance(options = {}) {
        if (!this.instance) {
            this.instance = new UnifiedDeviceDetector(options);
        }
        return this.instance;
    }
    
    constructor(options = {}) {
        // منع إنشاء مثيلات متعددة
        if (UnifiedDeviceDetector.instance) {
            return UnifiedDeviceDetector.instance;
        }
        
        // الخيارات الافتراضية المحسنة
        this.options = {
            mobileBreakpoint: 768,
            tabletBreakpoint: 1024,
            smallPhoneBreakpoint: 375,
            largePhoneBreakpoint: 414,
            applyClasses: true,
            enableLogging: true,
            ...options
        };

        // حالة الجهاز
        this.isMobile = false;
        this.isTablet = false;
        this.isDesktop = true;
        this.isSmallPhone = false;
        this.isLargePhone = false;
        this.deviceType = 'unknown';
        this.deviceBrand = 'unknown';
        this.screenWidth = window.innerWidth;
        this.screenHeight = window.innerHeight;
        this.pixelRatio = window.devicePixelRatio || 1;
        this.orientation = this.getOrientation();
        this.userAgent = navigator.userAgent || navigator.vendor || window.opera;
        this.touchSupport = 'ontouchstart' in window;

        // كلمات مفتاحية محسنة ومنظمة
        this.devicePatterns = {
            mobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile/i,
            android: /Android/i,
            iphone: /iPhone/i,
            ipad: /iPad/i,
            samsung: /Samsung|SM-|Galaxy/i,
            xiaomi: /Mi|Redmi|POCO|Xiaomi/i,
            huawei: /Huawei|Honor/i,
            oppo: /OPPO/i,
            vivo: /vivo/i,
            oneplus: /OnePlus/i
        };

        // تحديد نوع الجهاز
        this.detectDevice();
        
        // تطبيق الفئات إذا كان مطلوباً
        if (this.options.applyClasses) {
            this.applyDeviceClasses();
        }

        // إعداد مستمعي الأحداث
        this.setupEventListeners();
        
        // تسجيل معلومات الجهاز
        this.logDeviceInfo();
        
        UnifiedDeviceDetector.instance = this;
    }

    /**
     * الحصول على اتجاه الشاشة
     */
    getOrientation() {
        if (screen && screen.orientation) {
            return screen.orientation.angle === 0 || screen.orientation.angle === 180 ? 'portrait' : 'landscape';
        }
        return this.screenWidth > this.screenHeight ? 'landscape' : 'portrait';
    }

    /**
     * إعداد مستمعي الأحداث المحسنة
     */
    setupEventListeners() {
        // مستمع تغيير حجم النافذة مع تحسينات الأداء
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.detectDevice();
                if (this.options.applyClasses) {
                    this.applyDeviceClasses();
                }
                this.logDeviceInfo();
            }, 250);
        });

        // مستمع تغيير الاتجاه
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.detectDevice();
                if (this.options.applyClasses) {
                    this.applyDeviceClasses();
                }
                this.logDeviceInfo();
            }, 300);
        });
    }

    /**
     * كشف نوع الجهاز بطريقة محسنة وموثوقة
     */
    detectDevice() {
        // تحديث أبعاد الشاشة
        this.screenWidth = window.innerWidth;
        this.screenHeight = window.innerHeight;
        this.orientation = this.getOrientation();
        this.touchSupport = 'ontouchstart' in window;

        // كشف نوع الجهاز بناءً على حجم الشاشة
        const isVerySmallScreen = this.screenWidth < this.options.smallPhoneBreakpoint;
        const isSmallScreen = this.screenWidth < this.options.mobileBreakpoint;
        const isMediumScreen = this.screenWidth < this.options.tabletBreakpoint;
        const isLargeScreen = this.screenWidth >= this.options.largePhoneBreakpoint;

        // كشف نوع الجهاز من User Agent
        const isMobileUA = this.devicePatterns.mobile.test(this.userAgent);
        const isTabletUA = this.devicePatterns.ipad.test(this.userAgent);

        // تحديد العلامة التجارية
        this.detectDeviceBrand();

        // تحديث حالة الجهاز
        this.isMobile = isSmallScreen || (isMobileUA && !isTabletUA);
        this.isTablet = !this.isMobile && (isMediumScreen || isTabletUA);
        this.isDesktop = !this.isMobile && !this.isTablet;
        this.isSmallPhone = this.isMobile && isVerySmallScreen;
        this.isLargePhone = this.isMobile && isLargeScreen && !this.isSmallPhone;

        // تحديد نوع الجهاز النهائي
        if (this.isMobile) {
            this.deviceType = this.isSmallPhone ? 'small-phone' : 
                            this.isLargePhone ? 'large-phone' : 'mobile';
        } else if (this.isTablet) {
            this.deviceType = 'tablet';
        } else {
            this.deviceType = 'desktop';
        }

        return this.getDeviceInfo();
    }

    /**
     * كشف العلامة التجارية للجهاز
     */
    detectDeviceBrand() {
        if (this.devicePatterns.iphone.test(this.userAgent)) {
            this.deviceBrand = 'iPhone';
        } else if (this.devicePatterns.ipad.test(this.userAgent)) {
            this.deviceBrand = 'iPad';
        } else if (this.devicePatterns.samsung.test(this.userAgent)) {
            this.deviceBrand = 'Samsung';
        } else if (this.devicePatterns.xiaomi.test(this.userAgent)) {
            this.deviceBrand = 'Xiaomi';
        } else if (this.devicePatterns.huawei.test(this.userAgent)) {
            this.deviceBrand = 'Huawei';
        } else if (this.devicePatterns.oppo.test(this.userAgent)) {
            this.deviceBrand = 'OPPO';
        } else if (this.devicePatterns.vivo.test(this.userAgent)) {
            this.deviceBrand = 'Vivo';
        } else if (this.devicePatterns.oneplus.test(this.userAgent)) {
            this.deviceBrand = 'OnePlus';
        } else if (this.devicePatterns.android.test(this.userAgent)) {
            this.deviceBrand = 'Android';
        } else {
            this.deviceBrand = 'Unknown';
        }
    }

    /**
     * تطبيق فئات CSS المناسبة
     */
    applyDeviceClasses() {
        const body = document.body;
        const html = document.documentElement;

        // إزالة جميع فئات الأجهزة السابقة
        body.classList.remove(
            'mobile-device', 'tablet-device', 'desktop-device',
            'small-phone', 'large-phone', 'portrait-mode', 'landscape-mode',
            'iphone-device', 'android-device', 'samsung-device', 'xiaomi-device'
        );

        // إضافة فئة الاتجاه
        body.classList.add(this.orientation === 'portrait' ? 'portrait-mode' : 'landscape-mode');

        // إضافة فئة نوع الجهاز الرئيسي
        if (this.isMobile) {
            body.classList.add('mobile-device');
            
            if (this.isSmallPhone) {
                body.classList.add('small-phone');
            } else if (this.isLargePhone) {
                body.classList.add('large-phone');
            }

            // إضافة فئة العلامة التجارية
            if (this.deviceBrand === 'iPhone') {
                body.classList.add('iphone-device');
            } else if (this.deviceBrand === 'Samsung') {
                body.classList.add('samsung-device');
            } else if (this.deviceBrand === 'Xiaomi') {
                body.classList.add('xiaomi-device');
            } else if (this.deviceBrand === 'Android') {
                body.classList.add('android-device');
            }

            // تطبيق واجهة الهاتف
            this.applyMobileInterface();
            
        } else if (this.isTablet) {
            body.classList.add('tablet-device');
            this.applyTabletInterface();
        } else {
            body.classList.add('desktop-device');
            this.applyDesktopInterface();
        }

        // إضافة متغيرات CSS
        html.style.setProperty('--device-width', `${this.screenWidth}px`);
        html.style.setProperty('--device-height', `${this.screenHeight}px`);
        html.style.setProperty('--device-pixel-ratio', this.pixelRatio);
        html.style.setProperty('--device-type', `"${this.deviceType}"`);
        html.style.setProperty('--device-brand', `"${this.deviceBrand}"`);
    }

    /**
     * تطبيق واجهة الهاتف المحمول
     */
    applyMobileInterface() {
        const containerFluid = document.querySelector('.container-fluid');
        const mobileView = document.querySelector('.mobile-view');

        if (containerFluid) containerFluid.style.display = 'none';
        if (mobileView) mobileView.style.display = 'block';

        // تفعيل النظام المنفصل للهاتف
        if (window.mobileApp) {
            window.mobileApp.activate();
        }
    }

    /**
     * تطبيق واجهة الجهاز اللوحي
     */
    applyTabletInterface() {
        const containerFluid = document.querySelector('.container-fluid');
        const mobileView = document.querySelector('.mobile-view');

        if (containerFluid) containerFluid.style.display = 'block';
        if (mobileView) mobileView.style.display = 'none';
    }

    /**
     * تطبيق واجهة سطح المكتب
     */
    applyDesktopInterface() {
        const containerFluid = document.querySelector('.container-fluid');
        const mobileView = document.querySelector('.mobile-view');

        if (containerFluid) containerFluid.style.display = 'block';
        if (mobileView) mobileView.style.display = 'none';
    }

    /**
     * الحصول على معلومات الجهاز
     */
    getDeviceInfo() {
        return {
            isMobile: this.isMobile,
            isTablet: this.isTablet,
            isDesktop: this.isDesktop,
            isSmallPhone: this.isSmallPhone,
            isLargePhone: this.isLargePhone,
            deviceType: this.deviceType,
            deviceBrand: this.deviceBrand,
            screenWidth: this.screenWidth,
            screenHeight: this.screenHeight,
            orientation: this.orientation,
            touchSupport: this.touchSupport,
            pixelRatio: this.pixelRatio
        };
    }

    /**
     * تسجيل معلومات الجهاز
     */
    logDeviceInfo() {
        if (this.options.enableLogging) {
            console.log(`📱 Device Info: ${this.deviceType} (${this.deviceBrand}), Screen: ${this.screenWidth}x${this.screenHeight}, Touch: ${this.touchSupport}, Orientation: ${this.orientation}`);
        }
    }

    /**
     * التحقق من نوع الجهاز
     */
    static isMobile() {
        const detector = UnifiedDeviceDetector.getInstance();
        return detector.isMobile;
    }

    static isTablet() {
        const detector = UnifiedDeviceDetector.getInstance();
        return detector.isTablet;
    }

    static isDesktop() {
        const detector = UnifiedDeviceDetector.getInstance();
        return detector.isDesktop;
    }
}

// تهيئة النظام الموحد عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.unifiedDeviceDetector = UnifiedDeviceDetector.getInstance();
    console.log('🔧 Unified Device Detector initialized');
});
