/**
 * PerformanceOptimizer - نظام تحسين الأداء والاستجابة
 * يحسن أداء التطبيق خاصة على الأجهزة المحمولة
 * Created: 2024 - Performance System
 */
class PerformanceOptimizer {
    static instance = null;
    
    static getInstance() {
        if (!this.instance) {
            this.instance = new PerformanceOptimizer();
        }
        return this.instance;
    }
    
    constructor() {
        if (PerformanceOptimizer.instance) {
            return PerformanceOptimizer.instance;
        }
        
        // إعدادات الأداء
        this.settings = {
            enableVirtualScrolling: true,
            enableImageLazyLoading: true,
            enableDebouncing: true,
            enableThrottling: true,
            enableMemoryOptimization: true,
            debounceDelay: 300,
            throttleDelay: 100,
            maxCacheSize: 100,
            imageQuality: 0.8
        };
        
        // ذاكرة التخزين المؤقت للعناصر
        this.elementCache = new Map();
        this.imageCache = new Map();
        this.eventCache = new Map();
        
        // مراقبة الأداء
        this.performanceMetrics = {
            loadTime: 0,
            renderTime: 0,
            memoryUsage: 0,
            fps: 0,
            lastFrameTime: performance.now()
        };
        
        // مراقب التقاطع للتحميل الكسول
        this.intersectionObserver = null;
        
        PerformanceOptimizer.instance = this;
        this.init();
    }
    
    /**
     * تهيئة النظام
     */
    init() {
        console.log('⚡ Performance Optimizer initializing...');
        
        // قياس وقت التحميل
        this.measureLoadTime();
        
        // تحسين الصور
        if (this.settings.enableImageLazyLoading) {
            this.setupLazyLoading();
        }
        
        // تحسين الأحداث
        this.optimizeEvents();
        
        // مراقبة الأداء
        this.startPerformanceMonitoring();
        
        // تحسين الذاكرة
        if (this.settings.enableMemoryOptimization) {
            this.setupMemoryOptimization();
        }
        
        console.log('✅ Performance Optimizer initialized');
    }
    
    /**
     * قياس وقت التحميل
     */
    measureLoadTime() {
        const loadTime = performance.now();
        this.performanceMetrics.loadTime = loadTime;
        
        // قياس وقت تحميل DOM
        document.addEventListener('DOMContentLoaded', () => {
            const domLoadTime = performance.now();
            console.log(`📊 DOM loaded in ${domLoadTime.toFixed(2)}ms`);
        });
        
        // قياس وقت تحميل النافذة
        window.addEventListener('load', () => {
            const windowLoadTime = performance.now();
            console.log(`📊 Window loaded in ${windowLoadTime.toFixed(2)}ms`);
        });
    }
    
    /**
     * إعداد التحميل الكسول للصور
     */
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.intersectionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.loadImage(img);
                        this.intersectionObserver.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.1
            });
            
            // مراقبة الصور الموجودة
            this.observeImages();
            
            console.log('🖼️ Lazy loading enabled');
        }
    }
    
    /**
     * مراقبة الصور للتحميل الكسول
     */
    observeImages() {
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => {
            this.intersectionObserver.observe(img);
        });
    }
    
    /**
     * تحميل صورة
     */
    loadImage(img) {
        const src = img.dataset.src;
        if (src) {
            // إنشاء صورة جديدة للتحميل المسبق
            const newImg = new Image();
            newImg.onload = () => {
                img.src = src;
                img.classList.add('loaded');
                img.removeAttribute('data-src');
            };
            newImg.onerror = () => {
                img.classList.add('error');
                console.warn(`Failed to load image: ${src}`);
            };
            newImg.src = src;
        }
    }
    
    /**
     * تحسين الأحداث
     */
    optimizeEvents() {
        // تحسين أحداث التمرير
        this.optimizeScrollEvents();
        
        // تحسين أحداث تغيير الحجم
        this.optimizeResizeEvents();
        
        // تحسين أحداث الإدخال
        this.optimizeInputEvents();
        
        console.log('🎯 Events optimized');
    }
    
    /**
     * تحسين أحداث التمرير
     */
    optimizeScrollEvents() {
        let scrollTimeout;
        let isScrolling = false;
        
        const optimizedScrollHandler = this.throttle(() => {
            if (!isScrolling) {
                isScrolling = true;
                requestAnimationFrame(() => {
                    // معالجة التمرير
                    this.handleScroll();
                    isScrolling = false;
                });
            }
        }, this.settings.throttleDelay);
        
        window.addEventListener('scroll', optimizedScrollHandler, { passive: true });
    }
    
    /**
     * تحسين أحداث تغيير الحجم
     */
    optimizeResizeEvents() {
        const optimizedResizeHandler = this.debounce(() => {
            // معالجة تغيير الحجم
            this.handleResize();
        }, this.settings.debounceDelay);
        
        window.addEventListener('resize', optimizedResizeHandler);
    }
    
    /**
     * تحسين أحداث الإدخال
     */
    optimizeInputEvents() {
        const inputs = document.querySelectorAll('input[type="text"], input[type="search"], textarea');
        
        inputs.forEach(input => {
            const optimizedInputHandler = this.debounce((event) => {
                this.handleInput(event);
            }, this.settings.debounceDelay);
            
            input.addEventListener('input', optimizedInputHandler);
        });
    }
    
    /**
     * معالجة التمرير
     */
    handleScroll() {
        // تحديث الصور المرئية
        if (this.intersectionObserver) {
            this.observeImages();
        }
        
        // تحديث العناصر المرئية
        this.updateVisibleElements();
    }
    
    /**
     * معالجة تغيير الحجم
     */
    handleResize() {
        // تحديث أحجام الخرائط
        if (window.unifiedMapManager) {
            window.unifiedMapManager.invalidateAllMaps();
        }
        
        // تحديث العناصر المرنة
        this.updateResponsiveElements();
    }
    
    /**
     * معالجة الإدخال
     */
    handleInput(event) {
        const value = event.target.value;
        
        // تصفية المتاجر إذا كان حقل البحث
        if (event.target.id.includes('search')) {
            this.performSearch(value);
        }
    }
    
    /**
     * تنفيذ البحث
     */
    performSearch(query) {
        if (window.unifiedStateManager) {
            window.unifiedStateManager.filterStores(query);
        }
    }
    
    /**
     * تحديث العناصر المرئية
     */
    updateVisibleElements() {
        // تحديث قائمة المتاجر المرئية فقط
        const storeList = document.getElementById('mobile-store-list');
        if (storeList) {
            this.updateVirtualList(storeList);
        }
    }
    
    /**
     * تحديث العناصر المرنة
     */
    updateResponsiveElements() {
        // تحديث أحجام العناصر بناءً على حجم الشاشة
        const deviceDetector = window.unifiedDeviceDetector;
        if (deviceDetector) {
            deviceDetector.detectDevice();
        }
    }
    
    /**
     * تحديث القائمة الافتراضية
     */
    updateVirtualList(container) {
        if (!this.settings.enableVirtualScrolling) return;
        
        const items = container.children;
        const containerHeight = container.clientHeight;
        const scrollTop = container.scrollTop;
        const itemHeight = 80; // ارتفاع تقريبي للعنصر
        
        const startIndex = Math.floor(scrollTop / itemHeight);
        const endIndex = Math.min(startIndex + Math.ceil(containerHeight / itemHeight) + 1, items.length);
        
        // إخفاء العناصر غير المرئية
        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            if (i < startIndex || i > endIndex) {
                item.style.display = 'none';
            } else {
                item.style.display = 'block';
            }
        }
    }
    
    /**
     * بدء مراقبة الأداء
     */
    startPerformanceMonitoring() {
        // مراقبة FPS
        this.monitorFPS();
        
        // مراقبة استخدام الذاكرة
        this.monitorMemory();
        
        // تقرير دوري عن الأداء
        setInterval(() => {
            this.reportPerformance();
        }, 30000); // كل 30 ثانية
    }
    
    /**
     * مراقبة FPS
     */
    monitorFPS() {
        const measureFPS = () => {
            const now = performance.now();
            const delta = now - this.performanceMetrics.lastFrameTime;
            this.performanceMetrics.fps = 1000 / delta;
            this.performanceMetrics.lastFrameTime = now;
            
            requestAnimationFrame(measureFPS);
        };
        
        requestAnimationFrame(measureFPS);
    }
    
    /**
     * مراقبة الذاكرة
     */
    monitorMemory() {
        if ('memory' in performance) {
            setInterval(() => {
                this.performanceMetrics.memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024; // MB
            }, 5000);
        }
    }
    
    /**
     * إعداد تحسين الذاكرة
     */
    setupMemoryOptimization() {
        // تنظيف الذاكرة المؤقتة دورياً
        setInterval(() => {
            this.cleanupCache();
        }, 60000); // كل دقيقة
        
        // تنظيف عند إخفاء الصفحة
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.cleanupCache();
            }
        });
    }
    
    /**
     * تنظيف الذاكرة المؤقتة
     */
    cleanupCache() {
        // تنظيف ذاكرة العناصر
        if (this.elementCache.size > this.settings.maxCacheSize) {
            const keysToDelete = Array.from(this.elementCache.keys()).slice(0, this.elementCache.size - this.settings.maxCacheSize);
            keysToDelete.forEach(key => this.elementCache.delete(key));
        }
        
        // تنظيف ذاكرة الصور
        if (this.imageCache.size > this.settings.maxCacheSize) {
            const keysToDelete = Array.from(this.imageCache.keys()).slice(0, this.imageCache.size - this.settings.maxCacheSize);
            keysToDelete.forEach(key => this.imageCache.delete(key));
        }
        
        console.log('🧹 Cache cleaned up');
    }
    
    /**
     * تقرير الأداء
     */
    reportPerformance() {
        const metrics = this.performanceMetrics;
        console.log(`📊 Performance Report:
            FPS: ${metrics.fps.toFixed(1)}
            Memory: ${metrics.memoryUsage.toFixed(2)} MB
            Load Time: ${metrics.loadTime.toFixed(2)} ms`);
    }
    
    /**
     * دالة Debounce
     */
    debounce(func, delay) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }
    
    /**
     * دالة Throttle
     */
    throttle(func, delay) {
        let lastCall = 0;
        return function (...args) {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                return func.apply(this, args);
            }
        };
    }
    
    /**
     * تحسين صورة
     */
    optimizeImage(file, quality = null) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = () => {
                // تحديد الحجم الأمثل
                const maxWidth = 800;
                const maxHeight = 600;
                let { width, height } = img;
                
                if (width > maxWidth || height > maxHeight) {
                    const ratio = Math.min(maxWidth / width, maxHeight / height);
                    width *= ratio;
                    height *= ratio;
                }
                
                canvas.width = width;
                canvas.height = height;
                
                // رسم الصورة
                ctx.drawImage(img, 0, 0, width, height);
                
                // تحويل إلى Blob
                canvas.toBlob(resolve, 'image/jpeg', quality || this.settings.imageQuality);
            };
            
            img.src = URL.createObjectURL(file);
        });
    }
    
    /**
     * الحصول على إحصائيات الأداء
     */
    getPerformanceStats() {
        return {
            ...this.performanceMetrics,
            cacheSize: {
                elements: this.elementCache.size,
                images: this.imageCache.size,
                events: this.eventCache.size
            },
            settings: this.settings
        };
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.performanceOptimizer = PerformanceOptimizer.getInstance();
    console.log('⚡ Performance Optimizer ready');
});
