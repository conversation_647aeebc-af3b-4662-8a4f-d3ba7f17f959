/**
 * DeveloperConsole - لوحة تحكم للمطورين
 * توفر أدوات تشخيص ومراقبة النظام
 * Created: 2024 - Developer Tools
 */
class DeveloperConsole {
    static instance = null;
    
    static getInstance() {
        if (!this.instance) {
            this.instance = new DeveloperConsole();
        }
        return this.instance;
    }
    
    constructor() {
        if (DeveloperConsole.instance) {
            return DeveloperConsole.instance;
        }
        
        // إعدادات لوحة التحكم
        this.settings = {
            enabled: false,
            position: 'bottom-right',
            autoOpen: false,
            hotkey: 'F12'
        };
        
        // حالة لوحة التحكم
        this.isOpen = false;
        this.isMinimized = false;
        
        // عناصر الواجهة
        this.consoleElement = null;
        this.tabsContainer = null;
        this.contentContainer = null;
        this.currentTab = 'overview';
        
        DeveloperConsole.instance = this;
        
        // تفعيل لوحة التحكم في بيئة التطوير
        if (this.isDevelopmentMode()) {
            this.settings.enabled = true;
            this.init();
        }
    }
    
    /**
     * التحقق من بيئة التطوير
     */
    isDevelopmentMode() {
        return location.hostname === 'localhost' || 
               location.hostname === '127.0.0.1' ||
               location.search.includes('debug=true');
    }
    
    /**
     * تهيئة لوحة التحكم
     */
    init() {
        if (!this.settings.enabled) return;
        
        console.log('🛠️ Developer Console initializing...');
        
        // إنشاء واجهة لوحة التحكم
        this.createConsoleUI();
        
        // إعداد اختصارات لوحة المفاتيح
        this.setupKeyboardShortcuts();
        
        // إضافة أوامر عامة للنافذة
        this.addGlobalCommands();
        
        console.log('✅ Developer Console ready (Press Ctrl+Shift+D to open)');
    }
    
    /**
     * إنشاء واجهة لوحة التحكم
     */
    createConsoleUI() {
        // إنشاء العنصر الرئيسي
        this.consoleElement = document.createElement('div');
        this.consoleElement.id = 'developer-console';
        this.consoleElement.className = 'developer-console hidden';
        
        // إضافة HTML
        this.consoleElement.innerHTML = `
            <div class="console-header">
                <div class="console-title">
                    <i class="fas fa-code"></i>
                    Developer Console
                </div>
                <div class="console-controls">
                    <button class="console-btn" onclick="window.developerConsole.minimize()">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button class="console-btn" onclick="window.developerConsole.close()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="console-tabs">
                <button class="tab-btn active" data-tab="overview">نظرة عامة</button>
                <button class="tab-btn" data-tab="performance">الأداء</button>
                <button class="tab-btn" data-tab="logs">السجلات</button>
                <button class="tab-btn" data-tab="state">الحالة</button>
                <button class="tab-btn" data-tab="api">API</button>
                <button class="tab-btn" data-tab="tools">أدوات</button>
            </div>
            
            <div class="console-content">
                <div class="tab-content active" id="overview-tab">
                    <div class="overview-grid">
                        <div class="status-card">
                            <h4>حالة النظام</h4>
                            <div id="system-status">جاري التحميل...</div>
                        </div>
                        <div class="status-card">
                            <h4>معلومات الجهاز</h4>
                            <div id="device-info">جاري التحميل...</div>
                        </div>
                        <div class="status-card">
                            <h4>الأداء</h4>
                            <div id="performance-info">جاري التحميل...</div>
                        </div>
                        <div class="status-card">
                            <h4>الأخطاء الأخيرة</h4>
                            <div id="recent-errors">لا توجد أخطاء</div>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="performance-tab">
                    <div class="performance-metrics">
                        <div class="metric-row">
                            <span>FPS:</span>
                            <span id="fps-value">--</span>
                        </div>
                        <div class="metric-row">
                            <span>استخدام الذاكرة:</span>
                            <span id="memory-value">--</span>
                        </div>
                        <div class="metric-row">
                            <span>وقت التحميل:</span>
                            <span id="load-time-value">--</span>
                        </div>
                    </div>
                    <canvas id="performance-chart" width="400" height="200"></canvas>
                </div>
                
                <div class="tab-content" id="logs-tab">
                    <div class="logs-controls">
                        <select id="log-level-filter">
                            <option value="">جميع المستويات</option>
                            <option value="error">أخطاء</option>
                            <option value="warn">تحذيرات</option>
                            <option value="info">معلومات</option>
                            <option value="debug">تصحيح</option>
                        </select>
                        <button onclick="window.developerConsole.clearLogs()">مسح السجلات</button>
                    </div>
                    <div class="logs-container" id="logs-container">
                        جاري تحميل السجلات...
                    </div>
                </div>
                
                <div class="tab-content" id="state-tab">
                    <div class="state-viewer">
                        <pre id="state-content">جاري تحميل الحالة...</pre>
                    </div>
                </div>
                
                <div class="tab-content" id="api-tab">
                    <div class="api-stats">
                        <div class="stat-row">
                            <span>إجمالي الطلبات:</span>
                            <span id="total-requests">--</span>
                        </div>
                        <div class="stat-row">
                            <span>الطلبات الناجحة:</span>
                            <span id="successful-requests">--</span>
                        </div>
                        <div class="stat-row">
                            <span>الطلبات الفاشلة:</span>
                            <span id="failed-requests">--</span>
                        </div>
                        <div class="stat-row">
                            <span>معدل النجاح:</span>
                            <span id="success-rate">--</span>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="tools-tab">
                    <div class="tools-grid">
                        <button onclick="window.developerConsole.simulateMobile()">محاكاة الهاتف</button>
                        <button onclick="window.developerConsole.simulateDesktop()">محاكاة الكمبيوتر</button>
                        <button onclick="window.developerConsole.clearCache()">مسح الذاكرة المؤقتة</button>
                        <button onclick="window.developerConsole.exportLogs()">تصدير السجلات</button>
                        <button onclick="window.developerConsole.reloadMaps()">إعادة تحميل الخرائط</button>
                        <button onclick="window.developerConsole.testAPI()">اختبار API</button>
                    </div>
                </div>
            </div>
        `;
        
        // إضافة الأنماط
        this.addConsoleStyles();
        
        // إضافة إلى الصفحة
        document.body.appendChild(this.consoleElement);
        
        // إعداد التبويبات
        this.setupTabs();
        
        // بدء التحديث الدوري
        this.startPeriodicUpdates();
    }
    
    /**
     * إضافة أنماط لوحة التحكم
     */
    addConsoleStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .developer-console {
                position: fixed;
                bottom: 20px;
                right: 20px;
                width: 600px;
                height: 400px;
                background: #1e1e1e;
                color: #ffffff;
                border: 1px solid #333;
                border-radius: 8px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                z-index: 10000;
                box-shadow: 0 4px 20px rgba(0,0,0,0.5);
                resize: both;
                overflow: hidden;
            }
            
            .developer-console.hidden {
                display: none;
            }
            
            .developer-console.minimized {
                height: 40px;
            }
            
            .console-header {
                background: #333;
                padding: 8px 12px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid #555;
                cursor: move;
            }
            
            .console-title {
                font-weight: bold;
                color: #4CAF50;
            }
            
            .console-controls {
                display: flex;
                gap: 5px;
            }
            
            .console-btn {
                background: none;
                border: none;
                color: #fff;
                cursor: pointer;
                padding: 4px 8px;
                border-radius: 3px;
            }
            
            .console-btn:hover {
                background: #555;
            }
            
            .console-tabs {
                background: #2d2d2d;
                display: flex;
                border-bottom: 1px solid #555;
            }
            
            .tab-btn {
                background: none;
                border: none;
                color: #ccc;
                padding: 8px 12px;
                cursor: pointer;
                border-bottom: 2px solid transparent;
            }
            
            .tab-btn.active {
                color: #4CAF50;
                border-bottom-color: #4CAF50;
            }
            
            .tab-btn:hover {
                background: #3d3d3d;
            }
            
            .console-content {
                padding: 12px;
                height: calc(100% - 80px);
                overflow-y: auto;
            }
            
            .tab-content {
                display: none;
            }
            
            .tab-content.active {
                display: block;
            }
            
            .overview-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }
            
            .status-card {
                background: #2d2d2d;
                padding: 10px;
                border-radius: 4px;
                border: 1px solid #444;
            }
            
            .status-card h4 {
                margin: 0 0 8px 0;
                color: #4CAF50;
                font-size: 14px;
            }
            
            .metric-row, .stat-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 5px;
                padding: 2px 0;
            }
            
            .logs-container {
                background: #1a1a1a;
                border: 1px solid #444;
                border-radius: 4px;
                padding: 8px;
                height: 250px;
                overflow-y: auto;
                font-family: monospace;
                font-size: 11px;
            }
            
            .log-entry {
                margin-bottom: 2px;
                padding: 2px 4px;
                border-radius: 2px;
            }
            
            .log-entry.error {
                background: #4a1a1a;
                color: #ff6b6b;
            }
            
            .log-entry.warn {
                background: #4a3a1a;
                color: #ffd93d;
            }
            
            .log-entry.info {
                background: #1a2a4a;
                color: #74c0fc;
            }
            
            .state-viewer pre {
                background: #1a1a1a;
                border: 1px solid #444;
                border-radius: 4px;
                padding: 8px;
                height: 250px;
                overflow: auto;
                font-size: 10px;
                margin: 0;
            }
            
            .tools-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 8px;
            }
            
            .tools-grid button {
                background: #4CAF50;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 11px;
            }
            
            .tools-grid button:hover {
                background: #45a049;
            }
            
            .logs-controls {
                margin-bottom: 10px;
                display: flex;
                gap: 10px;
                align-items: center;
            }
            
            .logs-controls select,
            .logs-controls button {
                background: #333;
                color: white;
                border: 1px solid #555;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
            }
        `;
        
        document.head.appendChild(style);
    }
    
    /**
     * إعداد اختصارات لوحة المفاتيح
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl+Shift+D لفتح/إغلاق لوحة التحكم
            if (event.ctrlKey && event.shiftKey && event.key === 'D') {
                event.preventDefault();
                this.toggle();
            }
            
            // ESC لإغلاق لوحة التحكم
            if (event.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
    }
    
    /**
     * إضافة أوامر عامة
     */
    addGlobalCommands() {
        // إضافة أوامر للنافذة العامة
        window.devConsole = {
            open: () => this.open(),
            close: () => this.close(),
            toggle: () => this.toggle(),
            getSystemInfo: () => this.getSystemInfo(),
            exportData: () => this.exportAllData()
        };
    }
    
    /**
     * إعداد التبويبات
     */
    setupTabs() {
        const tabButtons = this.consoleElement.querySelectorAll('.tab-btn');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.dataset.tab;
                this.switchTab(tabName);
            });
        });
    }
    
    /**
     * تبديل التبويب
     */
    switchTab(tabName) {
        // إزالة الفئة النشطة من جميع التبويبات
        this.consoleElement.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        this.consoleElement.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        
        // تفعيل التبويب المحدد
        const activeButton = this.consoleElement.querySelector(`[data-tab="${tabName}"]`);
        const activeContent = this.consoleElement.querySelector(`#${tabName}-tab`);
        
        if (activeButton) activeButton.classList.add('active');
        if (activeContent) activeContent.classList.add('active');
        
        this.currentTab = tabName;
        
        // تحديث محتوى التبويب
        this.updateTabContent(tabName);
    }
    
    /**
     * بدء التحديث الدوري
     */
    startPeriodicUpdates() {
        setInterval(() => {
            if (this.isOpen && !this.isMinimized) {
                this.updateTabContent(this.currentTab);
            }
        }, 2000);
    }
    
    /**
     * تحديث محتوى التبويب
     */
    updateTabContent(tabName) {
        switch (tabName) {
            case 'overview':
                this.updateOverview();
                break;
            case 'performance':
                this.updatePerformance();
                break;
            case 'logs':
                this.updateLogs();
                break;
            case 'state':
                this.updateState();
                break;
            case 'api':
                this.updateAPI();
                break;
        }
    }
    
    /**
     * تحديث النظرة العامة
     */
    updateOverview() {
        // حالة النظام
        const systemStatus = window.systemMonitor?.getSystemStatus();
        if (systemStatus) {
            const statusElement = this.consoleElement.querySelector('#system-status');
            statusElement.innerHTML = `
                <div style="color: ${systemStatus.isHealthy ? '#4CAF50' : '#ff6b6b'}">
                    ${systemStatus.isHealthy ? '✅ صحي' : '❌ يحتاج انتباه'}
                </div>
                <small>آخر فحص: ${new Date(systemStatus.lastCheck).toLocaleTimeString()}</small>
            `;
        }
        
        // معلومات الجهاز
        const deviceInfo = window.unifiedDeviceDetector?.getDeviceInfo();
        if (deviceInfo) {
            const deviceElement = this.consoleElement.querySelector('#device-info');
            deviceElement.innerHTML = `
                <div>النوع: ${deviceInfo.deviceType}</div>
                <div>العلامة: ${deviceInfo.deviceBrand}</div>
                <div>الشاشة: ${deviceInfo.screenWidth}x${deviceInfo.screenHeight}</div>
            `;
        }
    }
    
    /**
     * فتح لوحة التحكم
     */
    open() {
        this.consoleElement.classList.remove('hidden');
        this.isOpen = true;
        this.isMinimized = false;
        this.updateTabContent(this.currentTab);
    }
    
    /**
     * إغلاق لوحة التحكم
     */
    close() {
        this.consoleElement.classList.add('hidden');
        this.isOpen = false;
    }
    
    /**
     * تبديل لوحة التحكم
     */
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }
    
    /**
     * تصغير لوحة التحكم
     */
    minimize() {
        this.consoleElement.classList.toggle('minimized');
        this.isMinimized = !this.isMinimized;
    }
    
    /**
     * الحصول على معلومات النظام
     */
    getSystemInfo() {
        return {
            device: window.unifiedDeviceDetector?.getDeviceInfo(),
            state: window.unifiedStateManager?.getFullState(),
            performance: window.performanceOptimizer?.getPerformanceStats(),
            api: window.unifiedAPIManager?.getStats(),
            monitor: window.systemMonitor?.getSystemStatus()
        };
    }
    
    /**
     * تصدير جميع البيانات
     */
    exportAllData() {
        const data = {
            timestamp: new Date().toISOString(),
            systemInfo: this.getSystemInfo(),
            logs: window.systemMonitor?.exportData()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `system-data-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }
}

// تهيئة لوحة التحكم
document.addEventListener('DOMContentLoaded', () => {
    window.developerConsole = DeveloperConsole.getInstance();
});
