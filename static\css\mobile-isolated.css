/**
 * أنماط منفصلة تماماً لواجهة الهاتف المحمول
 * لا تؤثر على واجهة الكمبيوتر نهائياً
 */

/* إخفاء واجهة الهاتف افتراضياً */
.mobile-view {
    display: none !important;
}

/* إظهار واجهة الهاتف فقط عند التفعيل */
body.mobile-active .mobile-view {
    display: block !important;
}

/* إخفاء واجهة الكمبيوتر عند تفعيل الهاتف */
body.mobile-active .container-fluid {
    display: none !important;
}

/* متغيرات خاصة بالهاتف فقط */
.mobile-view {
    --mobile-primary: #d50000;
    --mobile-secondary: #2575fc;
    --mobile-background: #f8f9fa;
    --mobile-text: #333;
    --mobile-border: #dee2e6;
    --mobile-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --mobile-radius: 8px;
    --mobile-header-height: 60px;
    --mobile-footer-height: 60px;
}

/* الهيكل الأساسي للهاتف */
.mobile-view {
    width: 100vw;
    height: 100vh;
    background-color: var(--mobile-background);
    color: var(--mobile-text);
    font-family: 'Tajawal', sans-serif;
    direction: rtl;
    overflow-x: hidden;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
}

/* رأس الهاتف */
.mobile-header {
    background-color: white;
    color: var(--mobile-text);
    padding: 0 20px;
    height: var(--mobile-header-height);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10000;
    box-shadow: var(--mobile-shadow);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--mobile-border);
}

/* شعار الهاتف */
.mobile-logo-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
}

.mobile-logo-wrapper h1 {
    font-size: 1.5rem;
    margin: 0;
    font-weight: 700;
    color: var(--mobile-primary);
    font-family: 'Arial', sans-serif;
}

.logo-circle-small-mobile {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--mobile-shadow);
    border: 1px solid var(--mobile-border);
}

.logo-circle-small-mobile i {
    font-size: 1.2rem;
    color: var(--mobile-primary);
}

/* محتوى الهاتف */
.mobile-content {
    padding-top: var(--mobile-header-height);
    padding-bottom: var(--mobile-footer-height);
    height: 100vh;
    overflow-y: auto;
    background-color: var(--mobile-background);
}

/* تبويبات الهاتف */
.mobile-tab-content {
    display: none;
    padding: 15px;
    min-height: calc(100vh - var(--mobile-header-height) - var(--mobile-footer-height));
}

.mobile-tab-content.active {
    display: block;
}

/* خريطة الهاتف */
#mobile-map {
    height: 60vh;
    width: 100%;
    border-radius: var(--mobile-radius);
    border: 1px solid var(--mobile-border);
    box-shadow: var(--mobile-shadow);
}

/* علامات الخريطة المخصصة */
.mobile-selected-marker {
    background: transparent !important;
    border: none !important;
}

.mobile-store-marker {
    background: transparent !important;
    border: none !important;
}

/* بطاقات المتاجر */
.mobile-store-card {
    background-color: white;
    border: 1px solid var(--mobile-border);
    border-radius: var(--mobile-radius);
    box-shadow: var(--mobile-shadow);
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.mobile-store-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.mobile-store-card .card-body {
    padding: 15px;
}

.mobile-store-card .card-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--mobile-text);
    margin-bottom: 5px;
}

.mobile-store-card .card-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 10px;
}

/* أزرار الهاتف */
.mobile-store-card .btn-group .btn {
    padding: 8px 12px;
    font-size: 0.875rem;
    border-radius: 0;
    transition: all 0.2s ease;
}

.mobile-store-card .btn-group .btn:first-child {
    border-top-right-radius: var(--mobile-radius);
    border-bottom-right-radius: var(--mobile-radius);
}

.mobile-store-card .btn-group .btn:last-child {
    border-top-left-radius: var(--mobile-radius);
    border-bottom-left-radius: var(--mobile-radius);
}

.mobile-store-card .btn-outline-primary {
    color: var(--mobile-primary);
    border-color: var(--mobile-primary);
}

.mobile-store-card .btn-outline-primary:hover {
    background-color: var(--mobile-primary);
    border-color: var(--mobile-primary);
    color: white;
}

.mobile-store-card .btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
}

.mobile-store-card .btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* نماذج الهاتف */
.mobile-form {
    background-color: white;
    border-radius: var(--mobile-radius);
    padding: 20px;
    box-shadow: var(--mobile-shadow);
    border: 1px solid var(--mobile-border);
}

.mobile-form .form-label {
    font-weight: 600;
    color: var(--mobile-text);
    margin-bottom: 5px;
}

.mobile-form .form-control,
.mobile-form .form-select {
    border-radius: var(--mobile-radius);
    border: 1px solid var(--mobile-border);
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.mobile-form .form-control:focus,
.mobile-form .form-select:focus {
    border-color: var(--mobile-primary);
    box-shadow: 0 0 0 0.2rem rgba(213, 0, 0, 0.25);
}

.mobile-form .btn {
    padding: 12px 20px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: var(--mobile-radius);
    transition: all 0.3s ease;
}

.mobile-form .btn-primary {
    background-color: var(--mobile-primary);
    border-color: var(--mobile-primary);
    color: white;
}

.mobile-form .btn-primary:hover {
    background-color: #b71c1c;
    border-color: #b71c1c;
}

.mobile-form .btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.mobile-form .btn-secondary:hover {
    background-color: #5a6268;
    border-color: #5a6268;
}

/* شريط التبويبات السفلي */
.mobile-tabs {
    background-color: white;
    border-top: 1px solid var(--mobile-border);
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10000;
    height: var(--mobile-footer-height);
    display: flex;
    align-items: center;
}

.mobile-tab-btn {
    background-color: transparent;
    color: #6c757d;
    border: none;
    padding: 8px 0;
    font-size: 0.75rem;
    transition: all 0.3s ease;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    position: relative;
    cursor: pointer;
}

.mobile-tab-btn i {
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.mobile-tab-btn.active {
    color: var(--mobile-primary);
}

.mobile-tab-btn.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--mobile-primary) 0%, var(--mobile-secondary) 100%);
}

.mobile-tab-btn:active {
    background-color: #f8f9fa;
    transform: scale(0.95);
}

/* معلومات الموقع */
.mobile-location-info {
    background-color: white;
    border-radius: var(--mobile-radius);
    padding: 15px;
    margin: 10px 0;
    box-shadow: var(--mobile-shadow);
    border: 1px solid var(--mobile-border);
}

.mobile-location-info .text-muted {
    font-size: 0.875rem;
    color: #6c757d;
}

/* أزرار الإجراءات */
.mobile-action-btn {
    background: linear-gradient(135deg, var(--mobile-primary) 0%, var(--mobile-secondary) 100%);
    color: white;
    border: none;
    border-radius: var(--mobile-radius);
    padding: 12px 20px;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: var(--mobile-shadow);
}

.mobile-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.mobile-action-btn:active {
    transform: translateY(0);
    box-shadow: var(--mobile-shadow);
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 375px) {
    .mobile-header {
        padding: 0 15px;
    }
    
    .mobile-logo-wrapper h1 {
        font-size: 1.3rem;
    }
    
    .mobile-tab-content {
        padding: 10px;
    }
    
    .mobile-form {
        padding: 15px;
    }
    
    .mobile-store-card .btn-group .btn {
        padding: 6px 8px;
        font-size: 0.8rem;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 414px) {
    .mobile-logo-wrapper h1 {
        font-size: 1.7rem;
    }
    
    .mobile-tab-content {
        padding: 20px;
    }
    
    .mobile-form {
        padding: 25px;
    }
}

/* تحسينات للاتجاه الأفقي */
@media (orientation: landscape) and (max-height: 500px) {
    #mobile-map {
        height: 40vh;
    }
    
    .mobile-header {
        height: 50px;
    }
    
    .mobile-tabs {
        height: 50px;
    }
    
    .mobile-content {
        padding-top: 50px;
        padding-bottom: 50px;
    }
}

/* تأثيرات الانتقال */
.mobile-view * {
    -webkit-tap-highlight-color: transparent;
}

.mobile-view button,
.mobile-view .btn {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* تحسينات الأداء */
.mobile-view {
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
    backface-visibility: hidden;
}

#mobile-map {
    transform: translateZ(0);
}

/* إخفاء عناصر غير ضرورية في الهاتف */
.mobile-view .d-md-block {
    display: none !important;
}

.mobile-view .d-lg-block {
    display: none !important;
}

/* تحسينات إضافية للنصوص */
.mobile-view {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
    font-smooth: always;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
