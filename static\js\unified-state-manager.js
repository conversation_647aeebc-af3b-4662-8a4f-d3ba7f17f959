/**
 * UnifiedStateManager - نظام موحد لإدارة حالة التطبيق
 * يدير البيانات والحالة بين واجهة الكمبيوتر والهاتف
 * Created: 2024 - Unified System
 */
class UnifiedStateManager {
    static instance = null;
    
    static getInstance() {
        if (!this.instance) {
            this.instance = new UnifiedStateManager();
        }
        return this.instance;
    }
    
    constructor() {
        if (UnifiedStateManager.instance) {
            return UnifiedStateManager.instance;
        }
        
        // حالة التطبيق
        this.state = {
            // معلومات الجهاز
            device: {
                type: 'unknown',
                isMobile: false,
                isTablet: false,
                isDesktop: true
            },
            
            // حالة الواجهة
            interface: {
                currentView: 'desktop', // desktop, mobile, tablet
                activeTab: null,
                isLoading: false
            },
            
            // بيانات المتاجر
            stores: {
                list: [],
                filtered: [],
                selected: [],
                currentListId: null,
                searchQuery: '',
                isLoading: false
            },
            
            // بيانات القوائم
            lists: {
                available: [],
                current: null,
                isLoading: false
            },
            
            // حالة الخرائط
            maps: {
                desktop: {
                    selectedLocation: null,
                    markers: [],
                    isInitialized: false
                },
                mobile: {
                    selectedLocation: null,
                    markers: [],
                    isInitialized: false
                }
            },
            
            // حالة النماذج
            forms: {
                store: {
                    isEditing: false,
                    currentStoreId: null,
                    data: {},
                    isValid: false
                }
            },
            
            // إعدادات المستخدم
            user: {
                isAuthenticated: false,
                role: null,
                preferences: {}
            }
        };
        
        // مستمعي التغييرات
        this.listeners = new Map();
        
        // تاريخ التغييرات للتراجع
        this.history = [];
        this.maxHistorySize = 50;
        
        UnifiedStateManager.instance = this;
        
        console.log('🔧 Unified State Manager initialized');
    }
    
    /**
     * تحديث حالة معينة
     */
    setState(path, value, notify = true) {
        const oldValue = this.getState(path);
        
        // حفظ الحالة السابقة في التاريخ
        this.saveToHistory(path, oldValue);
        
        // تحديث الحالة
        this.setNestedValue(this.state, path, value);
        
        // إشعار المستمعين
        if (notify) {
            this.notifyListeners(path, value, oldValue);
        }
        
        console.log(`📊 State updated: ${path} =`, value);
    }
    
    /**
     * الحصول على حالة معينة
     */
    getState(path) {
        return this.getNestedValue(this.state, path);
    }
    
    /**
     * الحصول على الحالة الكاملة
     */
    getFullState() {
        return { ...this.state };
    }
    
    /**
     * إضافة مستمع للتغييرات
     */
    subscribe(path, callback) {
        if (!this.listeners.has(path)) {
            this.listeners.set(path, []);
        }
        
        this.listeners.get(path).push(callback);
        
        // إرجاع دالة لإلغاء الاشتراك
        return () => {
            const callbacks = this.listeners.get(path);
            if (callbacks) {
                const index = callbacks.indexOf(callback);
                if (index > -1) {
                    callbacks.splice(index, 1);
                }
            }
        };
    }
    
    /**
     * إشعار المستمعين بالتغييرات
     */
    notifyListeners(path, newValue, oldValue) {
        // إشعار المستمعين المحددين
        const callbacks = this.listeners.get(path);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(newValue, oldValue, path);
                } catch (error) {
                    console.error(`Error in state listener for ${path}:`, error);
                }
            });
        }
        
        // إشعار المستمعين العامين
        const globalCallbacks = this.listeners.get('*');
        if (globalCallbacks) {
            globalCallbacks.forEach(callback => {
                try {
                    callback(path, newValue, oldValue);
                } catch (error) {
                    console.error(`Error in global state listener:`, error);
                }
            });
        }
    }
    
    /**
     * تحديث معلومات الجهاز
     */
    updateDeviceInfo(deviceInfo) {
        this.setState('device', deviceInfo);
        
        // تحديث الواجهة الحالية
        if (deviceInfo.isMobile) {
            this.setState('interface.currentView', 'mobile');
        } else if (deviceInfo.isTablet) {
            this.setState('interface.currentView', 'tablet');
        } else {
            this.setState('interface.currentView', 'desktop');
        }
    }
    
    /**
     * تحديث قائمة المتاجر
     */
    updateStores(stores) {
        this.setState('stores.list', stores);
        this.setState('stores.filtered', stores);
        this.setState('stores.isLoading', false);
    }
    
    /**
     * تصفية المتاجر
     */
    filterStores(query = '', listId = null) {
        const allStores = this.getState('stores.list');
        let filtered = [...allStores];
        
        // تصفية بالبحث
        if (query) {
            filtered = filtered.filter(store => 
                store.name.toLowerCase().includes(query.toLowerCase()) ||
                (store.phone && store.phone.includes(query))
            );
        }
        
        // تصفية بالقائمة
        if (listId) {
            filtered = filtered.filter(store => store.list_id == listId);
        }
        
        this.setState('stores.filtered', filtered);
        this.setState('stores.searchQuery', query);
        this.setState('stores.currentListId', listId);
    }
    
    /**
     * تحديد/إلغاء تحديد متجر
     */
    toggleStoreSelection(storeId) {
        const selected = this.getState('stores.selected');
        const index = selected.indexOf(storeId);
        
        if (index > -1) {
            selected.splice(index, 1);
        } else {
            selected.push(storeId);
        }
        
        this.setState('stores.selected', [...selected]);
    }
    
    /**
     * مسح تحديد المتاجر
     */
    clearStoreSelection() {
        this.setState('stores.selected', []);
    }
    
    /**
     * تحديث الموقع المحدد للخريطة
     */
    updateMapLocation(mapType, location) {
        this.setState(`maps.${mapType}.selectedLocation`, location);
    }
    
    /**
     * تحديث حالة النموذج
     */
    updateFormState(formType, data) {
        this.setState(`forms.${formType}`, { ...this.getState(`forms.${formType}`), ...data });
    }
    
    /**
     * بدء تحرير متجر
     */
    startEditingStore(storeId) {
        const store = this.getState('stores.list').find(s => s.id === storeId);
        if (store) {
            this.setState('forms.store', {
                isEditing: true,
                currentStoreId: storeId,
                data: { ...store },
                isValid: true
            });
        }
    }
    
    /**
     * إنهاء تحرير المتجر
     */
    stopEditingStore() {
        this.setState('forms.store', {
            isEditing: false,
            currentStoreId: null,
            data: {},
            isValid: false
        });
    }
    
    /**
     * تحديث بيانات المستخدم
     */
    updateUserInfo(userInfo) {
        this.setState('user', { ...this.getState('user'), ...userInfo });
    }
    
    /**
     * تعيين حالة التحميل
     */
    setLoading(path, isLoading) {
        this.setState(`${path}.isLoading`, isLoading);
    }
    
    /**
     * حفظ الحالة في التاريخ
     */
    saveToHistory(path, value) {
        this.history.push({
            path,
            value,
            timestamp: Date.now()
        });
        
        // الحفاظ على حجم التاريخ
        if (this.history.length > this.maxHistorySize) {
            this.history.shift();
        }
    }
    
    /**
     * التراجع عن آخر تغيير
     */
    undo() {
        if (this.history.length > 0) {
            const lastChange = this.history.pop();
            this.setNestedValue(this.state, lastChange.path, lastChange.value);
            this.notifyListeners(lastChange.path, lastChange.value, null);
            console.log(`↶ Undid change to ${lastChange.path}`);
        }
    }
    
    /**
     * مسح التاريخ
     */
    clearHistory() {
        this.history = [];
    }
    
    /**
     * تصدير الحالة
     */
    exportState() {
        return JSON.stringify(this.state, null, 2);
    }
    
    /**
     * استيراد الحالة
     */
    importState(stateJson) {
        try {
            const newState = JSON.parse(stateJson);
            this.state = newState;
            this.notifyListeners('*', newState, null);
            console.log('📥 State imported successfully');
        } catch (error) {
            console.error('❌ Failed to import state:', error);
        }
    }
    
    /**
     * إعادة تعيين الحالة
     */
    reset() {
        const defaultState = {
            device: { type: 'unknown', isMobile: false, isTablet: false, isDesktop: true },
            interface: { currentView: 'desktop', activeTab: null, isLoading: false },
            stores: { list: [], filtered: [], selected: [], currentListId: null, searchQuery: '', isLoading: false },
            lists: { available: [], current: null, isLoading: false },
            maps: {
                desktop: { selectedLocation: null, markers: [], isInitialized: false },
                mobile: { selectedLocation: null, markers: [], isInitialized: false }
            },
            forms: { store: { isEditing: false, currentStoreId: null, data: {}, isValid: false } },
            user: { isAuthenticated: false, role: null, preferences: {} }
        };
        
        this.state = defaultState;
        this.clearHistory();
        this.notifyListeners('*', defaultState, null);
        console.log('🔄 State reset to default');
    }
    
    /**
     * دالة مساعدة للوصول للقيم المتداخلة
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }
    
    /**
     * دالة مساعدة لتعيين القيم المتداخلة
     */
    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((current, key) => {
            if (!current[key]) current[key] = {};
            return current[key];
        }, obj);
        target[lastKey] = value;
    }
    
    /**
     * الحصول على إحصائيات الحالة
     */
    getStats() {
        return {
            totalStores: this.getState('stores.list').length,
            filteredStores: this.getState('stores.filtered').length,
            selectedStores: this.getState('stores.selected').length,
            availableLists: this.getState('lists.available').length,
            historySize: this.history.length,
            listenersCount: Array.from(this.listeners.values()).reduce((total, callbacks) => total + callbacks.length, 0)
        };
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.unifiedStateManager = UnifiedStateManager.getInstance();
    console.log('📊 Unified State Manager ready');
    
    // ربط النظام بكاشف الأجهزة
    if (window.unifiedDeviceDetector) {
        const deviceInfo = window.unifiedDeviceDetector.getDeviceInfo();
        window.unifiedStateManager.updateDeviceInfo(deviceInfo);
    }
});
